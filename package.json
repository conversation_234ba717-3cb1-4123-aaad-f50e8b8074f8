{"name": "galileo-site-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "next": "15.3.3", "leaflet": "^1.9.4", "react-leaflet": "^4.2.1", "lucide-react": "^0.263.1", "recharts": "^2.8.0", "jspdf": "^2.5.1", "html2canvas": "^1.4.1", "date-fns": "^2.30.0"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.3", "@eslint/eslintrc": "^3", "@types/leaflet": "^1.9.6"}}