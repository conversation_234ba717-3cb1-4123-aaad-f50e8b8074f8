"use client";

import { useEffect, useRef, useState } from "react";
import L from "leaflet";
import "leaflet/dist/leaflet.css";
import { LatLng, Polygon as PolygonType } from "../../types";

// Fix for default markers
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl:
    "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png",
  iconUrl:
    "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png",
  shadowUrl:
    "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png",
});

interface SimpleMapProps {
  center: LatLng;
  zoom: number;
  onPolygonComplete: (polygon: PolygonType) => void;
  selectedPolygon?: PolygonType;
  isDrawing: boolean;
  onDrawingChange: (isDrawing: boolean) => void;
  showDataLayers: {
    permits: boolean;
    zoning: boolean;
    competitors: boolean;
    landPrices: boolean;
  };
}

export default function SimpleMap({
  center,
  zoom,
  onPolygonComplete,
  selectedPolygon,
  isDrawing,
  onDrawingChange,
  showDataLayers,
}: SimpleMapProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<L.Map | null>(null);
  const [isMounted, setIsMounted] = useState(false);
  const [drawingPoints, setDrawingPoints] = useState<LatLng[]>([]);
  const drawingLayerRef = useRef<L.LayerGroup | null>(null);
  const selectedPolygonLayerRef = useRef<L.Polygon | null>(null);
  const dataLayersRef = useRef<L.LayerGroup | null>(null);

  // Use refs to store current values for event handlers
  const isDrawingRef = useRef(isDrawing);
  const drawingPointsRef = useRef(drawingPoints);

  // Update refs when props/state change
  useEffect(() => {
    isDrawingRef.current = isDrawing;
  }, [isDrawing]);

  useEffect(() => {
    drawingPointsRef.current = drawingPoints;
  }, [drawingPoints]);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (!isMounted || !mapRef.current || mapInstanceRef.current) return;

    // Initialize map
    const map = L.map(mapRef.current).setView([center.lat, center.lng], zoom);
    mapInstanceRef.current = map;

    // Add tile layer
    L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
      attribution:
        '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
    }).addTo(map);

    // Initialize layer groups
    drawingLayerRef.current = L.layerGroup().addTo(map);
    dataLayersRef.current = L.layerGroup().addTo(map);

    // Add click handler for drawing
    map.on("click", (e) => {
      if (isDrawingRef.current) {
        const newPoint = { lat: e.latlng.lat, lng: e.latlng.lng };
        setDrawingPoints((prev) => {
          const updated = [...prev, newPoint];
          updateDrawingLayer(updated);
          return updated;
        });
      }
    });

    // Add double-click handler to complete polygon
    map.on("dblclick", (e) => {
      if (isDrawingRef.current && drawingPointsRef.current.length >= 2) {
        const area = calculatePolygonArea(drawingPointsRef.current);
        const polygon: PolygonType = {
          id: `polygon-${Date.now()}`,
          coordinates: drawingPointsRef.current,
          area,
        };
        onPolygonComplete(polygon);
        setDrawingPoints([]);
        onDrawingChange(false);
        if (drawingLayerRef.current) {
          drawingLayerRef.current.clearLayers();
        }
      }
    });

    // Cleanup function
    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, [isMounted]);

  // Update drawing layer
  const updateDrawingLayer = (points: LatLng[]) => {
    if (!drawingLayerRef.current) return;

    drawingLayerRef.current.clearLayers();

    // Add markers for each point
    points.forEach((point, index) => {
      const marker = L.marker([point.lat, point.lng]).bindPopup(
        `Point ${index + 1}`
      );
      drawingLayerRef.current!.addLayer(marker);
    });

    // Add polygon if we have enough points
    if (points.length >= 3) {
      const polygon = L.polygon(
        points.map((p) => [p.lat, p.lng]),
        {
          color: "#3b82f6",
          fillColor: "#3b82f6",
          fillOpacity: 0.2,
          weight: 2,
          dashArray: "5, 5",
        }
      );
      drawingLayerRef.current!.addLayer(polygon);
    }
  };

  // Update selected polygon
  useEffect(() => {
    if (!mapInstanceRef.current) return;

    // Clear existing selected polygon
    if (selectedPolygonLayerRef.current) {
      mapInstanceRef.current.removeLayer(selectedPolygonLayerRef.current);
      selectedPolygonLayerRef.current = null;
    }

    // Add new selected polygon
    if (selectedPolygon) {
      const polygon = L.polygon(
        selectedPolygon.coordinates.map((coord) => [coord.lat, coord.lng]),
        {
          color: "#10b981",
          fillColor: "#10b981",
          fillOpacity: 0.3,
          weight: 3,
        }
      ).bindPopup(`
        <div>
          <h3 style="font-weight: bold; margin: 0 0 8px 0;">Selected Area</h3>
          <p style="margin: 0;">Area: ${(selectedPolygon.area / 4047).toFixed(
            2
          )} acres</p>
          <p style="margin: 0;">Area: ${(selectedPolygon.area / 10000).toFixed(
            2
          )} hectares</p>
        </div>
      `);

      selectedPolygonLayerRef.current = polygon;
      polygon.addTo(mapInstanceRef.current);
    }
  }, [selectedPolygon]);

  // Update data layers
  useEffect(() => {
    if (!dataLayersRef.current) return;

    dataLayersRef.current.clearLayers();

    // Mock data for demonstration
    const mockPermits = [
      {
        id: 1,
        lat: 37.7749,
        lng: -122.4194,
        type: "environmental",
        status: "approved",
      },
      {
        id: 2,
        lat: 37.7849,
        lng: -122.4094,
        type: "building",
        status: "pending",
      },
      {
        id: 3,
        lat: 37.7649,
        lng: -122.4294,
        type: "utility",
        status: "approved",
      },
    ];

    const mockCompetitors = [
      {
        id: 1,
        lat: 37.7549,
        lng: -122.4394,
        company: "NextEra Energy",
        type: "solar",
      },
      {
        id: 2,
        lat: 37.7949,
        lng: -122.3994,
        company: "Brookfield",
        type: "wind",
      },
    ];

    if (showDataLayers.permits) {
      mockPermits.forEach((permit) => {
        const color =
          permit.status === "approved"
            ? "#10b981"
            : permit.status === "pending"
            ? "#f59e0b"
            : "#ef4444";

        const marker = L.circleMarker([permit.lat, permit.lng], {
          radius: 8,
          fillColor: color,
          color: "#ffffff",
          weight: 2,
          opacity: 1,
          fillOpacity: 0.8,
        }).bindPopup(`
          <div>
            <h3 style="font-weight: bold; margin: 0 0 4px 0;">${permit.type} Permit</h3>
            <p style="margin: 0;">Status: ${permit.status}</p>
          </div>
        `);

        dataLayersRef.current!.addLayer(marker);
      });
    }

    if (showDataLayers.competitors) {
      mockCompetitors.forEach((competitor) => {
        const marker = L.circleMarker([competitor.lat, competitor.lng], {
          radius: 8,
          fillColor: "#8b5cf6",
          color: "#ffffff",
          weight: 2,
          opacity: 1,
          fillOpacity: 0.8,
        }).bindPopup(`
          <div>
            <h3 style="font-weight: bold; margin: 0 0 4px 0;">${competitor.company}</h3>
            <p style="margin: 0;">Type: ${competitor.type}</p>
          </div>
        `);

        dataLayersRef.current!.addLayer(marker);
      });
    }
  }, [showDataLayers]);

  // Clear drawing when not in drawing mode
  useEffect(() => {
    if (!isDrawing && drawingLayerRef.current) {
      drawingLayerRef.current.clearLayers();
      setDrawingPoints([]);
    }
  }, [isDrawing]);

  if (!isMounted) {
    return (
      <div className="w-full h-full bg-gray-200 flex items-center justify-center">
        <div className="text-gray-600">Loading map...</div>
      </div>
    );
  }

  return (
    <div className="w-full h-full relative">
      <div ref={mapRef} className="w-full h-full" style={{ zIndex: 0 }} />

      {isDrawing && (
        <div
          className="absolute top-4 left-4 bg-white p-3 rounded-lg shadow-lg"
          style={{ zIndex: 1000 }}
        >
          <div className="text-sm font-medium text-gray-900 mb-2">
            Drawing Mode Active
          </div>
          <div className="text-xs text-gray-600 space-y-1">
            <div>• Click to add points</div>
            <div>• Double-click to complete</div>
            <div>• Press ESC to cancel</div>
          </div>
        </div>
      )}
    </div>
  );
}

// Helper function to calculate polygon area using the shoelace formula
function calculatePolygonArea(coordinates: LatLng[]): number {
  if (coordinates.length < 3) return 0;

  let area = 0;
  const n = coordinates.length;

  for (let i = 0; i < n; i++) {
    const j = (i + 1) % n;
    area += coordinates[i].lng * coordinates[j].lat;
    area -= coordinates[j].lng * coordinates[i].lat;
  }

  area = Math.abs(area) / 2;

  // Convert from degrees to square meters (approximate)
  const metersPerDegree = 111320; // Approximate meters per degree at equator
  return area * metersPerDegree * metersPerDegree;
}
