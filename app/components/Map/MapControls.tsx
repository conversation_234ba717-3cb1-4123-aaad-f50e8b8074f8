'use client';

import { useState } from 'react';
import { 
  MapPin, 
  Square, 
  FileText, 
  Eye, 
  EyeOff, 
  Layers,
  Building,
  Zap,
  Users,
  DollarSign
} from 'lucide-react';

interface MapControlsProps {
  isDrawing: boolean;
  onStartDrawing: () => void;
  onGenerateReport: () => void;
  canGenerateReport: boolean;
  isGeneratingReport: boolean;
  showDataLayers: {
    permits: boolean;
    zoning: boolean;
    competitors: boolean;
    landPrices: boolean;
  };
  onToggleDataLayer: (layer: keyof MapControlsProps['showDataLayers']) => void;
}

export default function MapControls({
  isDrawing,
  onStartDrawing,
  onGenerateReport,
  canGenerateReport,
  isGeneratingReport,
  showDataLayers,
  onToggleDataLayer
}: MapControlsProps) {
  const [showLayerPanel, setShowLayerPanel] = useState(false);

  return (
    <div className="absolute top-4 right-4 z-10 space-y-2">
      {/* Main Controls */}
      <div className="bg-white rounded-lg shadow-lg p-2 space-y-2">
        <button
          onClick={onStartDrawing}
          disabled={isDrawing}
          className={`w-full flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
            isDrawing
              ? 'bg-blue-100 text-blue-700 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          <Square className="w-4 h-4" />
          {isDrawing ? 'Drawing...' : 'Select Area'}
        </button>

        <button
          onClick={onGenerateReport}
          disabled={!canGenerateReport || isGeneratingReport}
          className={`w-full flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
            !canGenerateReport || isGeneratingReport
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-green-600 text-white hover:bg-green-700'
          }`}
        >
          <FileText className="w-4 h-4" />
          {isGeneratingReport ? 'Generating...' : 'Generate Report'}
        </button>

        <button
          onClick={() => setShowLayerPanel(!showLayerPanel)}
          className="w-full flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors"
        >
          <Layers className="w-4 h-4" />
          Data Layers
        </button>
      </div>

      {/* Data Layers Panel */}
      {showLayerPanel && (
        <div className="bg-white rounded-lg shadow-lg p-3 space-y-3 min-w-[200px]">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-semibold text-gray-900">Data Layers</h3>
            <button
              onClick={() => setShowLayerPanel(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <EyeOff className="w-4 h-4" />
            </button>
          </div>

          <div className="space-y-2">
            <LayerToggle
              icon={<Building className="w-4 h-4" />}
              label="Permits"
              description="Building & environmental permits"
              isVisible={showDataLayers.permits}
              onToggle={() => onToggleDataLayer('permits')}
              color="green"
            />

            <LayerToggle
              icon={<MapPin className="w-4 h-4" />}
              label="Zoning"
              description="Land use classifications"
              isVisible={showDataLayers.zoning}
              onToggle={() => onToggleDataLayer('zoning')}
              color="blue"
            />

            <LayerToggle
              icon={<Zap className="w-4 h-4" />}
              label="Competitors"
              description="Existing energy projects"
              isVisible={showDataLayers.competitors}
              onToggle={() => onToggleDataLayer('competitors')}
              color="purple"
            />

            <LayerToggle
              icon={<DollarSign className="w-4 h-4" />}
              label="Land Prices"
              description="Recent land sales data"
              isVisible={showDataLayers.landPrices}
              onToggle={() => onToggleDataLayer('landPrices')}
              color="orange"
            />
          </div>
        </div>
      )}

      {/* Legend */}
      <div className="bg-white rounded-lg shadow-lg p-3 space-y-2">
        <h3 className="text-sm font-semibold text-gray-900">Legend</h3>
        <div className="space-y-1 text-xs">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-green-500"></div>
            <span>Approved Permits</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
            <span>Pending Permits</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-red-500"></div>
            <span>Rejected Permits</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-purple-500 border-2 border-white"></div>
            <span>Competitor Projects</span>
          </div>
        </div>
      </div>
    </div>
  );
}

interface LayerToggleProps {
  icon: React.ReactNode;
  label: string;
  description: string;
  isVisible: boolean;
  onToggle: () => void;
  color: 'green' | 'blue' | 'purple' | 'orange';
}

function LayerToggle({ 
  icon, 
  label, 
  description, 
  isVisible, 
  onToggle, 
  color 
}: LayerToggleProps) {
  const colorClasses = {
    green: 'text-green-600',
    blue: 'text-blue-600',
    purple: 'text-purple-600',
    orange: 'text-orange-600'
  };

  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-2 flex-1">
        <div className={`${colorClasses[color]}`}>
          {icon}
        </div>
        <div className="flex-1">
          <div className="text-sm font-medium text-gray-900">{label}</div>
          <div className="text-xs text-gray-500">{description}</div>
        </div>
      </div>
      <button
        onClick={onToggle}
        className={`p-1 rounded transition-colors ${
          isVisible 
            ? 'text-blue-600 hover:text-blue-700' 
            : 'text-gray-400 hover:text-gray-600'
        }`}
      >
        {isVisible ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
      </button>
    </div>
  );
}
