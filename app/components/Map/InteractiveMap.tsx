'use client';

import { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>y<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMapEvents } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { LatLng, Polygon as PolygonType } from '../../types';

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

interface InteractiveMapProps {
  center: LatLng;
  zoom: number;
  onPolygonComplete: (polygon: PolygonType) => void;
  selectedPolygon?: PolygonType;
  isDrawing: boolean;
  onDrawingChange: (isDrawing: boolean) => void;
  showDataLayers: {
    permits: boolean;
    zoning: boolean;
    competitors: boolean;
    landPrices: boolean;
  };
}

function DrawingHandler({ 
  isDrawing, 
  onPolygonComplete, 
  onDrawingChange 
}: {
  isDrawing: boolean;
  onPolygonComplete: (polygon: PolygonType) => void;
  onDrawingChange: (isDrawing: boolean) => void;
}) {
  const [drawingPoints, setDrawingPoints] = useState<LatLng[]>([]);
  
  useMapEvents({
    click: (e) => {
      if (isDrawing) {
        const newPoint = { lat: e.latlng.lat, lng: e.latlng.lng };
        setDrawingPoints(prev => [...prev, newPoint]);
      }
    },
    dblclick: (e) => {
      if (isDrawing && drawingPoints.length >= 3) {
        // Complete the polygon
        const area = calculatePolygonArea(drawingPoints);
        const polygon: PolygonType = {
          id: `polygon-${Date.now()}`,
          coordinates: drawingPoints,
          area
        };
        onPolygonComplete(polygon);
        setDrawingPoints([]);
        onDrawingChange(false);
      }
    },
    keydown: (e) => {
      if (e.originalEvent.key === 'Escape' && isDrawing) {
        setDrawingPoints([]);
        onDrawingChange(false);
      }
    }
  });

  return (
    <>
      {drawingPoints.length > 0 && (
        <Polygon
          positions={drawingPoints}
          pathOptions={{
            color: '#3b82f6',
            fillColor: '#3b82f6',
            fillOpacity: 0.2,
            weight: 2,
            dashArray: '5, 5'
          }}
        />
      )}
      {drawingPoints.map((point, index) => (
        <Marker key={index} position={[point.lat, point.lng]}>
          <Popup>Point {index + 1}</Popup>
        </Marker>
      ))}
    </>
  );
}

function DataLayersOverlay({ showDataLayers }: { showDataLayers: InteractiveMapProps['showDataLayers'] }) {
  // Mock data for demonstration - in real app, this would come from props or context
  const mockPermits = [
    { id: 1, lat: 37.7749, lng: -122.4194, type: 'environmental', status: 'approved' },
    { id: 2, lat: 37.7849, lng: -122.4094, type: 'building', status: 'pending' },
    { id: 3, lat: 37.7649, lng: -122.4294, type: 'utility', status: 'approved' }
  ];

  const mockCompetitors = [
    { id: 1, lat: 37.7549, lng: -122.4394, company: 'NextEra Energy', type: 'solar' },
    { id: 2, lat: 37.7949, lng: -122.3994, company: 'Brookfield', type: 'wind' }
  ];

  return (
    <>
      {showDataLayers.permits && mockPermits.map(permit => (
        <Marker 
          key={`permit-${permit.id}`} 
          position={[permit.lat, permit.lng]}
          icon={L.divIcon({
            className: 'custom-marker',
            html: `<div class="w-4 h-4 rounded-full ${
              permit.status === 'approved' ? 'bg-green-500' : 
              permit.status === 'pending' ? 'bg-yellow-500' : 'bg-red-500'
            }"></div>`,
            iconSize: [16, 16]
          })}
        >
          <Popup>
            <div>
              <h3 className="font-semibold">{permit.type} Permit</h3>
              <p>Status: {permit.status}</p>
            </div>
          </Popup>
        </Marker>
      ))}
      
      {showDataLayers.competitors && mockCompetitors.map(competitor => (
        <Marker 
          key={`competitor-${competitor.id}`} 
          position={[competitor.lat, competitor.lng]}
          icon={L.divIcon({
            className: 'custom-marker',
            html: `<div class="w-4 h-4 rounded-full bg-purple-500 border-2 border-white"></div>`,
            iconSize: [16, 16]
          })}
        >
          <Popup>
            <div>
              <h3 className="font-semibold">{competitor.company}</h3>
              <p>Type: {competitor.type}</p>
            </div>
          </Popup>
        </Marker>
      ))}
    </>
  );
}

export default function InteractiveMap({
  center,
  zoom,
  onPolygonComplete,
  selectedPolygon,
  isDrawing,
  onDrawingChange,
  showDataLayers
}: InteractiveMapProps) {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) {
    return (
      <div className="w-full h-full bg-gray-200 flex items-center justify-center">
        <div className="text-gray-600">Loading map...</div>
      </div>
    );
  }

  return (
    <div className="w-full h-full relative">
      <MapContainer
        center={[center.lat, center.lng]}
        zoom={zoom}
        style={{ height: '100%', width: '100%' }}
        className="z-0"
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />
        
        <DrawingHandler
          isDrawing={isDrawing}
          onPolygonComplete={onPolygonComplete}
          onDrawingChange={onDrawingChange}
        />
        
        <DataLayersOverlay showDataLayers={showDataLayers} />
        
        {selectedPolygon && (
          <Polygon
            positions={selectedPolygon.coordinates.map(coord => [coord.lat, coord.lng])}
            pathOptions={{
              color: '#10b981',
              fillColor: '#10b981',
              fillOpacity: 0.3,
              weight: 3
            }}
          >
            <Popup>
              <div>
                <h3 className="font-semibold">Selected Area</h3>
                <p>Area: {(selectedPolygon.area / 4047).toFixed(2)} acres</p>
                <p>Area: {(selectedPolygon.area / 10000).toFixed(2)} hectares</p>
              </div>
            </Popup>
          </Polygon>
        )}
      </MapContainer>
      
      {isDrawing && (
        <div className="absolute top-4 left-4 bg-white p-3 rounded-lg shadow-lg z-10">
          <div className="text-sm font-medium text-gray-900 mb-2">Drawing Mode Active</div>
          <div className="text-xs text-gray-600 space-y-1">
            <div>• Click to add points</div>
            <div>• Double-click to complete</div>
            <div>• Press ESC to cancel</div>
          </div>
        </div>
      )}
    </div>
  );
}

// Helper function to calculate polygon area using the shoelace formula
function calculatePolygonArea(coordinates: LatLng[]): number {
  if (coordinates.length < 3) return 0;
  
  let area = 0;
  const n = coordinates.length;
  
  for (let i = 0; i < n; i++) {
    const j = (i + 1) % n;
    area += coordinates[i].lng * coordinates[j].lat;
    area -= coordinates[j].lng * coordinates[i].lat;
  }
  
  area = Math.abs(area) / 2;
  
  // Convert from degrees to square meters (approximate)
  // This is a simplified calculation - in production, use proper geodesic calculations
  const metersPerDegree = 111320; // Approximate meters per degree at equator
  return area * metersPerDegree * metersPerDegree;
}
