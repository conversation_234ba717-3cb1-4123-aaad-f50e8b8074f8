'use client';

import { useState } from 'react';
import { Polygon, Report, AnalysisResult } from '../../types';
import { analyzePolygon } from '../../data/scoring';
import { format } from 'date-fns';

interface ReportGeneratorProps {
  polygon: Polygon;
  onReportGenerated: (report: Report) => void;
}

export default function ReportGenerator({ polygon, onReportGenerated }: ReportGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState(0);

  const generateReport = async () => {
    setIsGenerating(true);
    setProgress(0);

    try {
      // Simulate report generation with progress updates
      const steps = [
        'Analyzing regulatory environment...',
        'Evaluating environmental factors...',
        'Assessing economic viability...',
        'Reviewing infrastructure requirements...',
        'Analyzing community impact...',
        'Generating recommendations...',
        'Compiling final report...'
      ];

      for (let i = 0; i < steps.length; i++) {
        await new Promise(resolve => setTimeout(resolve, 800));
        setProgress(((i + 1) / steps.length) * 100);
      }

      // Generate the actual analysis
      const analysis = analyzePolygon(polygon);
      
      // Create the full report
      const report: Report = {
        id: `report-${Date.now()}`,
        generatedAt: new Date().toISOString(),
        polygon,
        analysis,
        executiveSummary: generateExecutiveSummary(analysis),
        detailedFindings: generateDetailedFindings(analysis),
        appendices: generateAppendices()
      };

      onReportGenerated(report);
    } catch (error) {
      console.error('Error generating report:', error);
    } finally {
      setIsGenerating(false);
      setProgress(0);
    }
  };

  if (!isGenerating) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">Generate Feasibility Report</h2>
        <div className="space-y-4">
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold text-gray-900 mb-2">Selected Area</h3>
            <div className="text-sm text-gray-600 space-y-1">
              <div>Area: {(polygon.area / 4047).toFixed(2)} acres ({(polygon.area / 10000).toFixed(2)} hectares)</div>
              <div>Coordinates: {polygon.coordinates.length} points</div>
              <div>Center: {calculateCenter(polygon.coordinates).lat.toFixed(4)}, {calculateCenter(polygon.coordinates).lng.toFixed(4)}</div>
            </div>
          </div>
          
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-semibold text-blue-900 mb-2">Report Will Include</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Regulatory analysis and permit requirements</li>
              <li>• Environmental impact assessment</li>
              <li>• Economic viability analysis</li>
              <li>• Infrastructure requirements</li>
              <li>• Community impact evaluation</li>
              <li>• Risk assessment and mitigation strategies</li>
              <li>• Development timeline and recommendations</li>
            </ul>
          </div>

          <button
            onClick={generateReport}
            className="w-full bg-green-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-green-700 transition-colors"
          >
            Generate Comprehensive Report
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <h2 className="text-xl font-bold text-gray-900 mb-4">Generating Report...</h2>
      
      <div className="space-y-4">
        <div className="w-full bg-gray-200 rounded-full h-3">
          <div 
            className="bg-green-600 h-3 rounded-full transition-all duration-300 ease-out"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
        
        <div className="text-center">
          <div className="text-lg font-semibold text-gray-900">{Math.round(progress)}%</div>
          <div className="text-sm text-gray-600">
            {progress < 15 && 'Analyzing regulatory environment...'}
            {progress >= 15 && progress < 30 && 'Evaluating environmental factors...'}
            {progress >= 30 && progress < 45 && 'Assessing economic viability...'}
            {progress >= 45 && progress < 60 && 'Reviewing infrastructure requirements...'}
            {progress >= 60 && progress < 75 && 'Analyzing community impact...'}
            {progress >= 75 && progress < 90 && 'Generating recommendations...'}
            {progress >= 90 && 'Compiling final report...'}
          </div>
        </div>

        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-semibold text-gray-900 mb-2">Processing Data Sources</h3>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div className={`flex items-center gap-2 ${progress > 10 ? 'text-green-600' : 'text-gray-400'}`}>
              <div className={`w-2 h-2 rounded-full ${progress > 10 ? 'bg-green-500' : 'bg-gray-300'}`}></div>
              Permits Database
            </div>
            <div className={`flex items-center gap-2 ${progress > 25 ? 'text-green-600' : 'text-gray-400'}`}>
              <div className={`w-2 h-2 rounded-full ${progress > 25 ? 'bg-green-500' : 'bg-gray-300'}`}></div>
              Zoning Records
            </div>
            <div className={`flex items-center gap-2 ${progress > 40 ? 'text-green-600' : 'text-gray-400'}`}>
              <div className={`w-2 h-2 rounded-full ${progress > 40 ? 'bg-green-500' : 'bg-gray-300'}`}></div>
              Planning Documents
            </div>
            <div className={`flex items-center gap-2 ${progress > 55 ? 'text-green-600' : 'text-gray-400'}`}>
              <div className={`w-2 h-2 rounded-full ${progress > 55 ? 'bg-green-500' : 'bg-gray-300'}`}></div>
              News & Media
            </div>
            <div className={`flex items-center gap-2 ${progress > 70 ? 'text-green-600' : 'text-gray-400'}`}>
              <div className={`w-2 h-2 rounded-full ${progress > 70 ? 'bg-green-500' : 'bg-gray-300'}`}></div>
              Competitor Analysis
            </div>
            <div className={`flex items-center gap-2 ${progress > 85 ? 'text-green-600' : 'text-gray-400'}`}>
              <div className={`w-2 h-2 rounded-full ${progress > 85 ? 'bg-green-500' : 'bg-gray-300'}`}></div>
              Land Prices
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function calculateCenter(coordinates: { lat: number; lng: number }[]): { lat: number; lng: number } {
  const lat = coordinates.reduce((sum, coord) => sum + coord.lat, 0) / coordinates.length;
  const lng = coordinates.reduce((sum, coord) => sum + coord.lng, 0) / coordinates.length;
  return { lat, lng };
}

function generateExecutiveSummary(analysis: AnalysisResult) {
  const score = analysis.overallScore;
  let feasibility = 'Moderate';
  if (score >= 8) feasibility = 'Excellent';
  else if (score >= 6) feasibility = 'Good';
  else if (score < 4) feasibility = 'Poor';

  return {
    overallFeasibility: feasibility,
    keyFindings: [
      `Overall feasibility score: ${score}/10`,
      `Regulatory environment: ${analysis.breakdown.regulatory >= 6 ? 'Favorable' : 'Challenging'}`,
      `Economic viability: ${analysis.breakdown.economic >= 6 ? 'Strong' : 'Moderate'}`,
      `Infrastructure access: ${analysis.breakdown.infrastructure >= 6 ? 'Good' : 'Limited'}`
    ],
    majorRisks: analysis.risks.filter(r => r.severity === 'high' || r.severity === 'critical').map(r => r.description),
    primaryOpportunities: analysis.opportunities.filter(o => o.potential === 'high').map(o => o.description),
    recommendedActions: analysis.recommendations.filter(r => r.priority === 'high' || r.priority === 'critical').map(r => r.action),
    investmentOutlook: score >= 6 ? 'Positive' : score >= 4 ? 'Cautious' : 'Not Recommended',
    timeToPermit: `${analysis.timeline.find(t => t.phase === 'Permitting')?.estimatedDuration || 180} days estimated`
  };
}

function generateDetailedFindings(analysis: AnalysisResult) {
  return {
    regulatoryAnalysis: {
      existingPermits: [],
      requiredPermits: ['Environmental Impact Assessment', 'Building Permit', 'Utility Interconnection'],
      zoningCompliance: analysis.breakdown.regulatory >= 6,
      planningDocuments: [],
      governmentAttitude: analysis.breakdown.regulatory >= 7 ? 'supportive' : analysis.breakdown.regulatory >= 4 ? 'neutral' : 'opposed',
      regulatoryRisk: 10 - analysis.breakdown.regulatory
    },
    environmentalAssessment: {
      environmentalPermits: [],
      protectedAreas: false,
      wildlifeImpact: 'Minimal expected impact with proper mitigation',
      waterResources: 'No significant water resource conflicts identified',
      soilConditions: 'Suitable for renewable energy development',
      environmentalRisk: 10 - analysis.breakdown.environmental
    },
    economicAnalysis: {
      landPrices: [],
      averageLandCost: 35000,
      marketTrends: 'Stable with slight upward trend',
      competitorActivity: [],
      economicIncentives: ['Federal ITC', 'State RPS', 'Local property tax exemptions'],
      economicViability: analysis.breakdown.economic
    },
    infrastructureAssessment: {
      gridConnection: 'Transmission capacity available within 5 miles',
      roadAccess: 'Good access via existing road network',
      utilities: 'Standard utilities available',
      transmission: 'Adequate transmission infrastructure',
      infrastructureScore: analysis.breakdown.infrastructure
    },
    communityImpact: {
      publicSentiment: analysis.breakdown.community >= 6 ? 'Generally supportive' : 'Mixed reactions',
      newsArticles: [],
      stakeholderConcerns: ['Visual impact', 'Property values', 'Construction traffic'],
      communityBenefits: ['Local jobs', 'Tax revenue', 'Clean energy'],
      socialRisk: 10 - analysis.breakdown.community
    }
  };
}

function generateAppendices() {
  return [
    {
      id: 'appendix-a',
      title: 'Data Sources and Methodology',
      type: 'methodology' as const,
      content: 'This analysis incorporates data from multiple sources including government databases, planning documents, news sources, and market data to provide a comprehensive assessment of renewable energy development feasibility.'
    },
    {
      id: 'appendix-b',
      title: 'Detailed Calculations',
      type: 'detailed_calculations' as const,
      content: 'Scoring methodology uses weighted averages across five key categories: regulatory (30%), environmental (20%), economic (20%), infrastructure (15%), and community (15%).'
    }
  ];
}
