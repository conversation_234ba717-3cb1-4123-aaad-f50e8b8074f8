'use client';

import { useState } from 'react';
import { Report } from '../../types';
import { 
  Download, 
  FileText, 
  AlertTriangle, 
  TrendingUp, 
  CheckCircle,
  Clock,
  DollarSign,
  Zap,
  Building,
  Users
} from 'lucide-react';
import { format } from 'date-fns';

interface ReportViewerProps {
  report: Report;
  onClose: () => void;
}

export default function ReportViewer({ report, onClose }: ReportViewerProps) {
  const [activeTab, setActiveTab] = useState<'summary' | 'detailed' | 'timeline'>('summary');

  const downloadReport = () => {
    // In a real application, this would generate and download a PDF
    const reportData = JSON.stringify(report, null, 2);
    const blob = new Blob([reportData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `feasibility-report-${report.id}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getScoreColor = (score: number) => {
    if (score >= 7) return 'text-green-600 bg-green-100';
    if (score >= 5) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const getScoreLabel = (score: number) => {
    if (score >= 8) return 'Excellent';
    if (score >= 6) return 'Good';
    if (score >= 4) return 'Fair';
    return 'Poor';
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gray-50 px-6 py-4 border-b flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Regulatory Feasibility Report</h1>
            <p className="text-sm text-gray-600">
              Generated on {format(new Date(report.generatedAt), 'PPP')} at {format(new Date(report.generatedAt), 'p')}
            </p>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={downloadReport}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Download className="w-4 h-4" />
              Download
            </button>
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
            >
              Close
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'summary', label: 'Executive Summary', icon: FileText },
              { id: 'detailed', label: 'Detailed Analysis', icon: Building },
              { id: 'timeline', label: 'Timeline & Next Steps', icon: Clock }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center gap-2 py-4 px-2 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {activeTab === 'summary' && (
            <div className="space-y-6">
              {/* Overall Score */}
              <div className="bg-gradient-to-r from-blue-50 to-green-50 p-6 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-xl font-bold text-gray-900">Overall Feasibility Score</h2>
                    <p className="text-gray-600">Comprehensive assessment across all factors</p>
                  </div>
                  <div className="text-center">
                    <div className={`text-4xl font-bold px-4 py-2 rounded-lg ${getScoreColor(report.analysis.overallScore)}`}>
                      {report.analysis.overallScore}/10
                    </div>
                    <div className="text-sm font-medium text-gray-600 mt-1">
                      {getScoreLabel(report.analysis.overallScore)}
                    </div>
                  </div>
                </div>
              </div>

              {/* Score Breakdown */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                {[
                  { key: 'regulatory', label: 'Regulatory', icon: Building, score: report.analysis.breakdown.regulatory },
                  { key: 'environmental', label: 'Environmental', icon: Zap, score: report.analysis.breakdown.environmental },
                  { key: 'economic', label: 'Economic', icon: DollarSign, score: report.analysis.breakdown.economic },
                  { key: 'infrastructure', label: 'Infrastructure', icon: Zap, score: report.analysis.breakdown.infrastructure },
                  { key: 'community', label: 'Community', icon: Users, score: report.analysis.breakdown.community }
                ].map(item => (
                  <div key={item.key} className="bg-white border rounded-lg p-4 text-center">
                    <item.icon className="w-6 h-6 mx-auto mb-2 text-gray-600" />
                    <div className="text-sm font-medium text-gray-900 mb-1">{item.label}</div>
                    <div className={`text-2xl font-bold px-2 py-1 rounded ${getScoreColor(item.score)}`}>
                      {item.score}
                    </div>
                  </div>
                ))}
              </div>

              {/* Key Findings */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-white border rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                    <CheckCircle className="w-5 h-5 text-green-600" />
                    Key Findings
                  </h3>
                  <ul className="space-y-2">
                    {report.executiveSummary.keyFindings.map((finding, index) => (
                      <li key={index} className="text-sm text-gray-700 flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                        {finding}
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="bg-white border rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                    <TrendingUp className="w-5 h-5 text-green-600" />
                    Primary Opportunities
                  </h3>
                  <ul className="space-y-2">
                    {report.executiveSummary.primaryOpportunities.map((opportunity, index) => (
                      <li key={index} className="text-sm text-gray-700 flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                        {opportunity}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              {/* Risks and Recommendations */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-white border rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                    <AlertTriangle className="w-5 h-5 text-red-600" />
                    Major Risks
                  </h3>
                  {report.executiveSummary.majorRisks.length > 0 ? (
                    <ul className="space-y-2">
                      {report.executiveSummary.majorRisks.map((risk, index) => (
                        <li key={index} className="text-sm text-gray-700 flex items-start gap-2">
                          <div className="w-1.5 h-1.5 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                          {risk}
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-sm text-gray-500">No major risks identified</p>
                  )}
                </div>

                <div className="bg-white border rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                    <CheckCircle className="w-5 h-5 text-blue-600" />
                    Recommended Actions
                  </h3>
                  <ul className="space-y-2">
                    {report.executiveSummary.recommendedActions.map((action, index) => (
                      <li key={index} className="text-sm text-gray-700 flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                        {action}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              {/* Investment Outlook */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-1">Investment Outlook</h3>
                    <p className="text-lg font-medium text-blue-600">{report.executiveSummary.investmentOutlook}</p>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-1">Estimated Time to Permit</h3>
                    <p className="text-lg font-medium text-green-600">{report.executiveSummary.timeToPermit}</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'detailed' && (
            <div className="space-y-6">
              <h2 className="text-xl font-bold text-gray-900">Detailed Analysis</h2>
              
              {/* Regulatory Analysis */}
              <div className="bg-white border rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Regulatory Analysis</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Zoning Compliance</h4>
                    <p className={`text-sm px-2 py-1 rounded ${
                      report.detailedFindings.regulatoryAnalysis.zoningCompliance 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {report.detailedFindings.regulatoryAnalysis.zoningCompliance ? 'Compliant' : 'Non-compliant'}
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Government Attitude</h4>
                    <p className="text-sm text-gray-700 capitalize">
                      {report.detailedFindings.regulatoryAnalysis.governmentAttitude}
                    </p>
                  </div>
                </div>
                <div className="mt-4">
                  <h4 className="font-medium text-gray-900 mb-2">Required Permits</h4>
                  <ul className="text-sm text-gray-700 space-y-1">
                    {report.detailedFindings.regulatoryAnalysis.requiredPermits.map((permit, index) => (
                      <li key={index} className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                        {permit}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              {/* Economic Analysis */}
              <div className="bg-white border rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Economic Analysis</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Average Land Cost</h4>
                    <p className="text-lg font-semibold text-green-600">
                      ${report.detailedFindings.economicAnalysis.averageLandCost.toLocaleString()}/acre
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Market Trends</h4>
                    <p className="text-sm text-gray-700">{report.detailedFindings.economicAnalysis.marketTrends}</p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Economic Viability</h4>
                    <div className={`text-lg font-semibold px-2 py-1 rounded ${getScoreColor(report.detailedFindings.economicAnalysis.economicViability)}`}>
                      {report.detailedFindings.economicAnalysis.economicViability}/10
                    </div>
                  </div>
                </div>
                <div className="mt-4">
                  <h4 className="font-medium text-gray-900 mb-2">Available Incentives</h4>
                  <div className="flex flex-wrap gap-2">
                    {report.detailedFindings.economicAnalysis.economicIncentives.map((incentive, index) => (
                      <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                        {incentive}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'timeline' && (
            <div className="space-y-6">
              <h2 className="text-xl font-bold text-gray-900">Development Timeline</h2>
              
              <div className="space-y-4">
                {report.analysis.timeline.map((phase, index) => (
                  <div key={phase.id} className="bg-white border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold text-gray-900">{phase.phase}</h3>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-600">{phase.estimatedDuration} days</span>
                        {phase.criticalPath && (
                          <span className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded">Critical Path</span>
                        )}
                      </div>
                    </div>
                    <p className="text-sm text-gray-700 mb-2">{phase.description}</p>
                    {phase.dependencies.length > 0 && (
                      <div className="text-xs text-gray-500">
                        Dependencies: {phase.dependencies.join(', ')}
                      </div>
                    )}
                  </div>
                ))}
              </div>

              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="font-semibold text-blue-900 mb-2">Total Estimated Timeline</h3>
                <p className="text-lg font-bold text-blue-800">
                  {Math.ceil(report.analysis.timeline.reduce((total, phase) => total + phase.estimatedDuration, 0) / 30)} months
                </p>
                <p className="text-sm text-blue-700">
                  ({report.analysis.timeline.reduce((total, phase) => total + phase.estimatedDuration, 0)} days)
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
