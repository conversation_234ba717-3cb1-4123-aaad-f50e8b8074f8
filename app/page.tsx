'use client';

import { useState } from 'react';
import dynamic from 'next/dynamic';
import { Polygon, Report, MapState } from './types';
import MapControls from './components/Map/MapControls';
import ReportGenerator from './components/Reports/ReportGenerator';
import ReportViewer from './components/Reports/ReportViewer';
import { Zap, MapPin, FileText, TrendingUp } from 'lucide-react';

// Dynamically import the map component to avoid SSR issues
const InteractiveMap = dynamic(() => import('./components/Map/InteractiveMap'), {
  ssr: false,
  loading: () => (
    <div className="w-full h-full bg-gray-200 flex items-center justify-center">
      <div className="text-gray-600">Loading map...</div>
    </div>
  )
});

export default function Home() {
  const [mapState, setMapState] = useState<MapState>({
    center: { lat: 37.7749, lng: -122.4194 }, // San Francisco Bay Area
    zoom: 10,
    isDrawing: false,
    showDataLayers: {
      permits: true,
      zoning: false,
      competitors: false,
      landPrices: false
    }
  });

  const [selectedPolygon, setSelectedPolygon] = useState<Polygon | undefined>();
  const [currentReport, setCurrentReport] = useState<Report | undefined>();
  const [isGeneratingReport, setIsGeneratingReport] = useState(false);
  const [showReportGenerator, setShowReportGenerator] = useState(false);

  const handleStartDrawing = () => {
    setMapState(prev => ({ ...prev, isDrawing: true }));
    setSelectedPolygon(undefined);
    setShowReportGenerator(false);
  };

  const handlePolygonComplete = (polygon: Polygon) => {
    setSelectedPolygon(polygon);
    setMapState(prev => ({ ...prev, isDrawing: false }));
    setShowReportGenerator(true);
  };

  const handleDrawingChange = (isDrawing: boolean) => {
    setMapState(prev => ({ ...prev, isDrawing }));
  };

  const handleGenerateReport = () => {
    if (selectedPolygon) {
      setIsGeneratingReport(true);
      setShowReportGenerator(true);
    }
  };

  const handleReportGenerated = (report: Report) => {
    setCurrentReport(report);
    setIsGeneratingReport(false);
    setShowReportGenerator(false);
  };

  const handleToggleDataLayer = (layer: keyof MapState['showDataLayers']) => {
    setMapState(prev => ({
      ...prev,
      showDataLayers: {
        ...prev.showDataLayers,
        [layer]: !prev.showDataLayers[layer]
      }
    }));
  };

  return (
    <div className="h-screen flex flex-col bg-gray-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-3">
              <div className="bg-green-600 p-2 rounded-lg">
                <Zap className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">GreenSite Analyzer</h1>
                <p className="text-sm text-gray-600">Renewable Energy Site Assessment Platform</p>
              </div>
            </div>
            <div className="flex items-center gap-6 text-sm text-gray-600">
              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4" />
                <span>Interactive Mapping</span>
              </div>
              <div className="flex items-center gap-2">
                <FileText className="w-4 h-4" />
                <span>Automated Reports</span>
              </div>
              <div className="flex items-center gap-2">
                <TrendingUp className="w-4 h-4" />
                <span>Feasibility Scoring</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Map Container */}
        <div className="flex-1 relative">
          <InteractiveMap
            center={mapState.center}
            zoom={mapState.zoom}
            onPolygonComplete={handlePolygonComplete}
            selectedPolygon={selectedPolygon}
            isDrawing={mapState.isDrawing}
            onDrawingChange={handleDrawingChange}
            showDataLayers={mapState.showDataLayers}
          />
          
          <MapControls
            isDrawing={mapState.isDrawing}
            onStartDrawing={handleStartDrawing}
            onGenerateReport={handleGenerateReport}
            canGenerateReport={!!selectedPolygon && !isGeneratingReport}
            isGeneratingReport={isGeneratingReport}
            showDataLayers={mapState.showDataLayers}
            onToggleDataLayer={handleToggleDataLayer}
          />
        </div>

        {/* Sidebar */}
        {(showReportGenerator || currentReport) && (
          <div className="w-96 bg-white border-l overflow-y-auto">
            {showReportGenerator && selectedPolygon && !currentReport && (
              <ReportGenerator
                polygon={selectedPolygon}
                onReportGenerated={handleReportGenerated}
              />
            )}
          </div>
        )}
      </div>

      {/* Report Viewer Modal */}
      {currentReport && (
        <ReportViewer
          report={currentReport}
          onClose={() => setCurrentReport(undefined)}
        />
      )}

      {/* Welcome Message */}
      {!selectedPolygon && !showReportGenerator && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg shadow-xl p-8 max-w-md text-center z-10">
          <div className="bg-green-100 p-3 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
            <Zap className="w-8 h-8 text-green-600" />
          </div>
          <h2 className="text-xl font-bold text-gray-900 mb-2">Welcome to GreenSite Analyzer</h2>
          <p className="text-gray-600 mb-6">
            Assess the regulatory feasibility of land plots for renewable energy projects. 
            Start by selecting an area on the map to analyze.
          </p>
          <button
            onClick={handleStartDrawing}
            className="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors"
          >
            Start Site Analysis
          </button>
        </div>
      )}
    </div>
  );
}
