import { Polygon, AnalysisResult, Risk, Opportunity, Recommendation, TimelineItem } from '../types';
import { getPermitsInArea, getPermitsByStatus } from './permits';
import { getZoningForLocation, isRenewableEnergyAllowed } from './zoning';
import { getPlanningDocumentsForArea, calculatePlanningScore } from './planning';
import { getNewsForArea, calculateNewsSentimentScore } from './news';
import { getCompetitorsInArea, calculateCompetitionDensity } from './competitors';
import { getLandPricesInArea, calculateAverageLandPrice, estimateLandCostForProject } from './landPrices';

export function analyzePolygon(polygon: Polygon): AnalysisResult {
  // Calculate polygon center for analysis
  const center = calculatePolygonCenter(polygon.coordinates);
  const radiusKm = Math.sqrt(polygon.area / Math.PI) / 1000; // Convert area to approximate radius in km
  
  // Gather all relevant data
  const permits = getPermitsInArea(center, radiusKm * 2);
  const zoning = getZoningForLocation(center.lat, center.lng);
  const planningDocs = getPlanningDocumentsForArea(center, radiusKm * 3);
  const news = getNewsForArea(center, radiusKm * 5);
  const competitors = getCompetitorsInArea(center, radiusKm * 10);
  const landPrices = getLandPricesInArea(center, radiusKm * 2);
  
  // Calculate individual scores
  const regulatoryScore = calculateRegulatoryScore(permits, zoning, planningDocs);
  const environmentalScore = calculateEnvironmentalScore(permits, zoning);
  const economicScore = calculateEconomicScore(landPrices, competitors, center, radiusKm);
  const infrastructureScore = calculateInfrastructureScore(center, permits);
  const communityScore = calculateCommunityScore(news, planningDocs);
  
  // Calculate overall score
  const overallScore = (
    regulatoryScore * 0.3 +
    environmentalScore * 0.2 +
    economicScore * 0.2 +
    infrastructureScore * 0.15 +
    communityScore * 0.15
  );
  
  // Generate risks, opportunities, and recommendations
  const risks = generateRisks(permits, zoning, news, competitors);
  const opportunities = generateOpportunities(permits, planningDocs, news, landPrices);
  const recommendations = generateRecommendations(overallScore, risks, opportunities);
  const timeline = generateTimeline(permits, zoning, overallScore);
  
  return {
    polygon,
    overallScore: Math.round(overallScore * 10) / 10,
    breakdown: {
      regulatory: Math.round(regulatoryScore * 10) / 10,
      environmental: Math.round(environmentalScore * 10) / 10,
      economic: Math.round(economicScore * 10) / 10,
      infrastructure: Math.round(infrastructureScore * 10) / 10,
      community: Math.round(communityScore * 10) / 10
    },
    risks,
    opportunities,
    recommendations,
    timeline
  };
}

function calculatePolygonCenter(coordinates: { lat: number; lng: number }[]): { lat: number; lng: number } {
  const lat = coordinates.reduce((sum, coord) => sum + coord.lat, 0) / coordinates.length;
  const lng = coordinates.reduce((sum, coord) => sum + coord.lng, 0) / coordinates.length;
  return { lat, lng };
}

function calculateRegulatoryScore(permits: any[], zoning: any, planningDocs: any[]): number {
  let score = 5; // Start with neutral score
  
  // Zoning compatibility
  if (zoning && isRenewableEnergyAllowed(zoning.id)) {
    score += 2;
  } else if (zoning) {
    score -= 1;
  }
  
  // Existing permits
  const approvedPermits = permits.filter(p => p.status === 'approved');
  const rejectedPermits = permits.filter(p => p.status === 'rejected');
  
  score += approvedPermits.length * 0.5;
  score -= rejectedPermits.length * 1;
  
  // Planning documents impact
  const planningScore = calculatePlanningScore(planningDocs);
  score += (planningScore - 5) * 0.3; // Adjust based on planning sentiment
  
  return Math.max(0, Math.min(10, score));
}

function calculateEnvironmentalScore(permits: any[], zoning: any): number {
  let score = 6; // Start slightly positive for renewable energy
  
  // Environmental permits
  const envPermits = permits.filter(p => p.type === 'environmental');
  const approvedEnvPermits = envPermits.filter(p => p.status === 'approved');
  const rejectedEnvPermits = envPermits.filter(p => p.status === 'rejected');
  
  score += approvedEnvPermits.length * 1;
  score -= rejectedEnvPermits.length * 2;
  
  // Zoning environmental factors
  if (zoning) {
    if (zoning.type === 'agricultural' || zoning.type === 'industrial') {
      score += 1; // Generally better for renewable energy
    }
    if (zoning.restrictions.some((r: string) => r.toLowerCase().includes('environmental'))) {
      score -= 0.5;
    }
  }
  
  return Math.max(0, Math.min(10, score));
}

function calculateEconomicScore(landPrices: any[], competitors: any[], center: any, radiusKm: number): number {
  let score = 5;
  
  // Land price analysis
  if (landPrices.length > 0) {
    const avgPrices = calculateAverageLandPrice(landPrices);
    // Lower land prices are better for projects
    if (avgPrices.averagePricePerAcre < 30000) {
      score += 2;
    } else if (avgPrices.averagePricePerAcre < 50000) {
      score += 1;
    } else if (avgPrices.averagePricePerAcre > 80000) {
      score -= 2;
    }
  }
  
  // Competition analysis
  const competitionData = calculateCompetitionDensity(center, radiusKm * 5);
  if (competitionData.competitionScore < 3) {
    score += 1; // Low competition is good
  } else if (competitionData.competitionScore > 7) {
    score -= 1; // High competition is challenging
  }
  
  return Math.max(0, Math.min(10, score));
}

function calculateInfrastructureScore(center: any, permits: any[]): number {
  let score = 5;
  
  // Utility permits indicate good grid access
  const utilityPermits = permits.filter(p => p.type === 'utility');
  score += utilityPermits.length * 0.5;
  
  // Distance from urban areas (simplified - in real app would use actual infrastructure data)
  // This is a placeholder - would integrate with actual infrastructure databases
  score += 1; // Assume reasonable infrastructure access
  
  return Math.max(0, Math.min(10, score));
}

function calculateCommunityScore(news: any[], planningDocs: any[]): number {
  let score = 5;
  
  // News sentiment
  const newsScore = calculateNewsSentimentScore(news);
  score += (newsScore - 5) * 0.4;
  
  // Planning document sentiment
  const supportiveDocs = planningDocs.filter(d => d.impact === 'positive');
  const opposingDocs = planningDocs.filter(d => d.impact === 'negative');
  
  score += supportiveDocs.length * 0.5;
  score -= opposingDocs.length * 0.5;
  
  return Math.max(0, Math.min(10, score));
}

function generateRisks(permits: any[], zoning: any, news: any[], competitors: any[]): Risk[] {
  const risks: Risk[] = [];
  
  // Regulatory risks
  const rejectedPermits = permits.filter(p => p.status === 'rejected');
  if (rejectedPermits.length > 0) {
    risks.push({
      id: 'risk-regulatory-001',
      category: 'regulatory',
      severity: 'high',
      probability: 0.7,
      description: 'Previous permit rejections in the area indicate potential regulatory challenges',
      mitigation: 'Conduct thorough pre-application consultation with regulatory authorities',
      impact: 8
    });
  }
  
  // Environmental risks
  if (zoning && zoning.restrictions.some((r: string) => r.toLowerCase().includes('environmental'))) {
    risks.push({
      id: 'risk-environmental-001',
      category: 'environmental',
      severity: 'medium',
      probability: 0.5,
      description: 'Zoning restrictions indicate potential environmental sensitivities',
      mitigation: 'Conduct comprehensive environmental impact assessment early in development',
      impact: 6
    });
  }
  
  // Community risks
  const negativeNews = news.filter(n => n.sentiment === 'negative');
  if (negativeNews.length > 2) {
    risks.push({
      id: 'risk-social-001',
      category: 'social',
      severity: 'medium',
      probability: 0.6,
      description: 'Recent negative news coverage suggests potential community opposition',
      mitigation: 'Implement comprehensive community engagement and benefit-sharing program',
      impact: 7
    });
  }
  
  // Economic risks
  const competitionData = calculateCompetitionDensity({ lat: 37.7749, lng: -122.4194 }, 10);
  if (competitionData.competitionScore > 7) {
    risks.push({
      id: 'risk-economic-001',
      category: 'economic',
      severity: 'medium',
      probability: 0.8,
      description: 'High competition density may impact project economics and grid access',
      mitigation: 'Differentiate project through innovative technology or community partnerships',
      impact: 6
    });
  }
  
  return risks;
}

function generateOpportunities(permits: any[], planningDocs: any[], news: any[], landPrices: any[]): Opportunity[] {
  const opportunities: Opportunity[] = [];
  
  // Policy opportunities
  const supportiveDocs = planningDocs.filter(d => d.impact === 'positive');
  if (supportiveDocs.length > 2) {
    opportunities.push({
      id: 'opp-policy-001',
      category: 'regulatory',
      potential: 'high',
      description: 'Strong policy support for renewable energy development in the region',
      actionRequired: 'Align project with regional clean energy goals and apply for available incentives',
      timeframe: '6-12 months',
      value: 8
    });
  }
  
  // Economic opportunities
  if (landPrices.length > 0) {
    const avgPrices = calculateAverageLandPrice(landPrices);
    if (avgPrices.averagePricePerAcre < 30000) {
      opportunities.push({
        id: 'opp-economic-001',
        category: 'economic',
        potential: 'high',
        description: 'Below-market land prices provide favorable project economics',
        actionRequired: 'Secure land rights quickly before prices increase',
        timeframe: '3-6 months',
        value: 9
      });
    }
  }
  
  // Technology opportunities
  const positiveNews = news.filter(n => n.sentiment === 'positive' && n.tags.includes('innovation'));
  if (positiveNews.length > 0) {
    opportunities.push({
      id: 'opp-tech-001',
      category: 'technical',
      potential: 'medium',
      description: 'Growing interest in innovative renewable energy technologies',
      actionRequired: 'Consider advanced technologies like agrivoltaics or floating solar',
      timeframe: '12-18 months',
      value: 7
    });
  }
  
  return opportunities;
}

function generateRecommendations(overallScore: number, risks: Risk[], opportunities: Opportunity[]): Recommendation[] {
  const recommendations: Recommendation[] = [];
  
  if (overallScore >= 7) {
    recommendations.push({
      id: 'rec-001',
      priority: 'high',
      category: 'Development Strategy',
      action: 'Proceed with full feasibility study and site development',
      rationale: 'High overall feasibility score indicates strong potential for successful project',
      timeframe: 'Immediate',
      cost: 'medium',
      impact: 9
    });
  } else if (overallScore >= 5) {
    recommendations.push({
      id: 'rec-002',
      priority: 'medium',
      category: 'Risk Mitigation',
      action: 'Address identified risks before proceeding with development',
      rationale: 'Moderate feasibility score requires risk mitigation to improve project viability',
      timeframe: '3-6 months',
      cost: 'medium',
      impact: 7
    });
  } else {
    recommendations.push({
      id: 'rec-003',
      priority: 'low',
      category: 'Alternative Assessment',
      action: 'Consider alternative sites or project configurations',
      rationale: 'Low feasibility score suggests significant challenges with current approach',
      timeframe: '6-12 months',
      cost: 'low',
      impact: 5
    });
  }
  
  // Add specific recommendations based on risks
  const highRisks = risks.filter(r => r.severity === 'high' || r.severity === 'critical');
  if (highRisks.length > 0) {
    recommendations.push({
      id: 'rec-004',
      priority: 'critical',
      category: 'Risk Management',
      action: 'Develop comprehensive risk mitigation plan for identified high-severity risks',
      rationale: 'High-severity risks could significantly impact project success',
      timeframe: 'Immediate',
      cost: 'high',
      impact: 8
    });
  }
  
  return recommendations;
}

function generateTimeline(permits: any[], zoning: any, overallScore: number): TimelineItem[] {
  const timeline: TimelineItem[] = [];
  
  // Base timeline phases
  timeline.push({
    id: 'phase-001',
    phase: 'Site Assessment',
    description: 'Detailed site surveys, environmental studies, and technical assessments',
    estimatedDuration: 90,
    dependencies: [],
    criticalPath: true
  });
  
  timeline.push({
    id: 'phase-002',
    phase: 'Permitting',
    description: 'Submit and process all required permits and approvals',
    estimatedDuration: 180,
    dependencies: ['phase-001'],
    criticalPath: true
  });
  
  timeline.push({
    id: 'phase-003',
    phase: 'Financing',
    description: 'Secure project financing and finalize investment agreements',
    estimatedDuration: 120,
    dependencies: ['phase-002'],
    criticalPath: true
  });
  
  timeline.push({
    id: 'phase-004',
    phase: 'Construction',
    description: 'Site preparation, equipment installation, and commissioning',
    estimatedDuration: 365,
    dependencies: ['phase-003'],
    criticalPath: true
  });
  
  // Adjust timeline based on complexity
  if (overallScore < 5) {
    // Add additional phases for challenging projects
    timeline.splice(1, 0, {
      id: 'phase-001b',
      phase: 'Risk Mitigation',
      description: 'Address identified risks and challenges before proceeding',
      estimatedDuration: 120,
      dependencies: ['phase-001'],
      criticalPath: true
    });
    
    // Update dependencies
    timeline.find(p => p.id === 'phase-002')!.dependencies = ['phase-001b'];
  }
  
  return timeline;
}
