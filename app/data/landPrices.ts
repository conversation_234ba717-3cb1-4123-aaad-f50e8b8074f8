import { LandPrice } from '../types';

export const mockLandPrices: LandPrice[] = [
  {
    id: 'price-001',
    location: { lat: 37.7749, lng: -122.4194 },
    pricePerAcre: 85000,
    pricePerSqMeter: 210,
    date: '2024-02-15',
    source: 'Bay Area Land Sales Database',
    landType: 'Urban Commercial',
    saleType: 'sale'
  },
  {
    id: 'price-002',
    location: { lat: 37.7649, lng: -122.4294 },
    pricePerAcre: 45000,
    pricePerSqMeter: 111,
    date: '2024-01-20',
    source: 'County Assessor Records',
    landType: 'Industrial',
    saleType: 'sale'
  },
  {
    id: 'price-003',
    location: { lat: 37.7549, lng: -122.4394 },
    pricePerAcre: 25000,
    pricePerSqMeter: 62,
    date: '2023-12-10',
    source: 'Agricultural Land Brokers',
    landType: 'Agricultural',
    saleType: 'lease'
  },
  {
    id: 'price-004',
    location: { lat: 37.7849, lng: -122.4094 },
    pricePerAcre: 65000,
    pricePerSqMeter: 161,
    date: '2024-03-05',
    source: 'Commercial Real Estate Listings',
    landType: 'Mixed Use',
    saleType: 'sale'
  },
  {
    id: 'price-005',
    location: { lat: 37.7949, lng: -122.3994 },
    pricePerAcre: 35000,
    pricePerSqMeter: 86,
    date: '2024-01-30',
    source: 'Rural Land Exchange',
    landType: 'Rural Industrial',
    saleType: 'option'
  },
  {
    id: 'price-006',
    location: { lat: 37.7449, lng: -122.4494 },
    pricePerAcre: 18000,
    pricePerSqMeter: 44,
    date: '2023-11-25',
    source: 'Farm Bureau Land Sales',
    landType: 'Rangeland',
    saleType: 'lease'
  },
  {
    id: 'price-007',
    location: { lat: 37.8049, lng: -122.3894 },
    pricePerAcre: 55000,
    pricePerSqMeter: 136,
    date: '2024-02-28',
    source: 'County Development Authority',
    landType: 'Suburban Commercial',
    saleType: 'sale'
  },
  {
    id: 'price-008',
    location: { lat: 37.7349, lng: -122.4594 },
    pricePerAcre: 22000,
    pricePerSqMeter: 54,
    date: '2024-01-15',
    source: 'Agricultural Cooperative',
    landType: 'Agricultural',
    saleType: 'lease'
  },
  {
    id: 'price-009',
    location: { lat: 37.7149, lng: -122.4794 },
    pricePerAcre: 28000,
    pricePerSqMeter: 69,
    date: '2023-12-20',
    source: 'Land Investment Trust',
    landType: 'Rural Development',
    saleType: 'sale'
  },
  {
    id: 'price-010',
    location: { lat: 37.8149, lng: -122.3794 },
    pricePerAcre: 75000,
    pricePerSqMeter: 185,
    date: '2024-03-10',
    source: 'Municipal Land Records',
    landType: 'Urban Industrial',
    saleType: 'sale'
  },
  {
    id: 'price-011',
    location: { lat: 37.7049, lng: -122.4894 },
    pricePerAcre: 32000,
    pricePerSqMeter: 79,
    date: '2024-02-05',
    source: 'Energy Development Consortium',
    landType: 'Energy Zone',
    saleType: 'lease'
  },
  {
    id: 'price-012',
    location: { lat: 37.6949, lng: -122.4994 },
    pricePerAcre: 20000,
    pricePerSqMeter: 49,
    date: '2023-10-30',
    source: 'Ranch Land Specialists',
    landType: 'Grazing Land',
    saleType: 'lease'
  },
  {
    id: 'price-013',
    location: { lat: 37.6849, lng: -122.5094 },
    pricePerAcre: 40000,
    pricePerSqMeter: 99,
    date: '2024-01-25',
    source: 'Wine Country Land Sales',
    landType: 'Agricultural Premium',
    saleType: 'sale'
  },
  {
    id: 'price-014',
    location: { lat: 37.8249, lng: -122.3694 },
    pricePerAcre: 15000,
    pricePerSqMeter: 37,
    date: '2023-09-15',
    source: 'Forestry Land Exchange',
    landType: 'Forestland',
    saleType: 'lease'
  },
  {
    id: 'price-015',
    location: { lat: 37.6749, lng: -122.5194 },
    pricePerAcre: 38000,
    pricePerSqMeter: 94,
    date: '2024-02-20',
    source: 'Regional Development Board',
    landType: 'Rural Commercial',
    saleType: 'option'
  },
  {
    id: 'price-016',
    location: { lat: 37.7250, lng: -122.4650 },
    pricePerAcre: 30000,
    pricePerSqMeter: 74,
    date: '2024-03-01',
    source: 'Renewable Energy Land Bank',
    landType: 'Energy Development',
    saleType: 'lease'
  },
  {
    id: 'price-017',
    location: { lat: 37.7750, lng: -122.4150 },
    pricePerAcre: 95000,
    pricePerSqMeter: 235,
    date: '2024-02-10',
    source: 'Urban Development Authority',
    landType: 'Urban Prime',
    saleType: 'sale'
  },
  {
    id: 'price-018',
    location: { lat: 37.7450, lng: -122.4450 },
    pricePerAcre: 26000,
    pricePerSqMeter: 64,
    date: '2023-11-30',
    source: 'Agricultural Land Trust',
    landType: 'Farmland',
    saleType: 'lease'
  },
  {
    id: 'price-019',
    location: { lat: 37.7850, lng: -122.4050 },
    pricePerAcre: 70000,
    pricePerSqMeter: 173,
    date: '2024-01-05',
    source: 'Commercial Property Exchange',
    landType: 'Commercial',
    saleType: 'sale'
  },
  {
    id: 'price-020',
    location: { lat: 37.7650, lng: -122.4350 },
    pricePerAcre: 42000,
    pricePerSqMeter: 104,
    date: '2024-02-25',
    source: 'Industrial Development Corp',
    landType: 'Light Industrial',
    saleType: 'option'
  }
];

export function getLandPricesInArea(center: { lat: number; lng: number }, radiusKm: number): LandPrice[] {
  return mockLandPrices.filter(price => {
    const distance = calculateDistance(center, price.location);
    return distance <= radiusKm;
  });
}

export function getLandPricesByType(landType: string): LandPrice[] {
  return mockLandPrices.filter(price => 
    price.landType.toLowerCase().includes(landType.toLowerCase())
  );
}

export function getLandPricesBySaleType(saleType: LandPrice['saleType']): LandPrice[] {
  return mockLandPrices.filter(price => price.saleType === saleType);
}

export function getRecentLandPrices(monthsBack: number = 6): LandPrice[] {
  const cutoffDate = new Date();
  cutoffDate.setMonth(cutoffDate.getMonth() - monthsBack);
  
  return mockLandPrices.filter(price => {
    const priceDate = new Date(price.date);
    return priceDate >= cutoffDate;
  });
}

export function calculateAverageLandPrice(prices: LandPrice[]): {
  averagePricePerAcre: number;
  averagePricePerSqMeter: number;
  medianPricePerAcre: number;
  medianPricePerSqMeter: number;
  priceRange: {
    minPerAcre: number;
    maxPerAcre: number;
    minPerSqMeter: number;
    maxPerSqMeter: number;
  };
} {
  if (prices.length === 0) {
    return {
      averagePricePerAcre: 0,
      averagePricePerSqMeter: 0,
      medianPricePerAcre: 0,
      medianPricePerSqMeter: 0,
      priceRange: {
        minPerAcre: 0,
        maxPerAcre: 0,
        minPerSqMeter: 0,
        maxPerSqMeter: 0
      }
    };
  }
  
  const pricesPerAcre = prices.map(p => p.pricePerAcre).sort((a, b) => a - b);
  const pricesPerSqMeter = prices.map(p => p.pricePerSqMeter).sort((a, b) => a - b);
  
  const averagePricePerAcre = pricesPerAcre.reduce((sum, price) => sum + price, 0) / prices.length;
  const averagePricePerSqMeter = pricesPerSqMeter.reduce((sum, price) => sum + price, 0) / prices.length;
  
  const medianPricePerAcre = pricesPerAcre[Math.floor(pricesPerAcre.length / 2)];
  const medianPricePerSqMeter = pricesPerSqMeter[Math.floor(pricesPerSqMeter.length / 2)];
  
  return {
    averagePricePerAcre,
    averagePricePerSqMeter,
    medianPricePerAcre,
    medianPricePerSqMeter,
    priceRange: {
      minPerAcre: pricesPerAcre[0],
      maxPerAcre: pricesPerAcre[pricesPerAcre.length - 1],
      minPerSqMeter: pricesPerSqMeter[0],
      maxPerSqMeter: pricesPerSqMeter[pricesPerSqMeter.length - 1]
    }
  };
}

export function getLandPriceTrends(area: { lat: number; lng: number }, radiusKm: number, monthsBack: number = 12): {
  monthlyAverages: { month: string; averagePrice: number }[];
  trend: 'increasing' | 'decreasing' | 'stable';
  changePercent: number;
} {
  const areaPrice = getLandPricesInArea(area, radiusKm);
  const cutoffDate = new Date();
  cutoffDate.setMonth(cutoffDate.getMonth() - monthsBack);
  
  const recentPrices = areaPrice.filter(price => {
    const priceDate = new Date(price.date);
    return priceDate >= cutoffDate;
  });
  
  // Group by month
  const monthlyData: Record<string, number[]> = {};
  recentPrices.forEach(price => {
    const month = price.date.substring(0, 7); // YYYY-MM format
    if (!monthlyData[month]) {
      monthlyData[month] = [];
    }
    monthlyData[month].push(price.pricePerAcre);
  });
  
  // Calculate monthly averages
  const monthlyAverages = Object.entries(monthlyData)
    .map(([month, prices]) => ({
      month,
      averagePrice: prices.reduce((sum, price) => sum + price, 0) / prices.length
    }))
    .sort((a, b) => a.month.localeCompare(b.month));
  
  // Determine trend
  let trend: 'increasing' | 'decreasing' | 'stable' = 'stable';
  let changePercent = 0;
  
  if (monthlyAverages.length >= 2) {
    const firstPrice = monthlyAverages[0].averagePrice;
    const lastPrice = monthlyAverages[monthlyAverages.length - 1].averagePrice;
    changePercent = ((lastPrice - firstPrice) / firstPrice) * 100;
    
    if (changePercent > 5) {
      trend = 'increasing';
    } else if (changePercent < -5) {
      trend = 'decreasing';
    }
  }
  
  return {
    monthlyAverages,
    trend,
    changePercent
  };
}

export function estimateLandCostForProject(
  area: { lat: number; lng: number }, 
  projectSizeAcres: number, 
  projectType: 'solar' | 'wind' | 'storage'
): {
  estimatedCost: number;
  costPerAcre: number;
  confidence: 'high' | 'medium' | 'low';
  factors: string[];
} {
  const nearbyPrices = getLandPricesInArea(area, 10); // 10km radius
  const recentPrices = getRecentLandPrices(12);
  const relevantPrices = nearbyPrices.filter(price => recentPrices.includes(price));
  
  let basePrice = 0;
  let confidence: 'high' | 'medium' | 'low' = 'low';
  const factors: string[] = [];
  
  if (relevantPrices.length >= 5) {
    confidence = 'high';
    factors.push('Sufficient recent comparable sales');
  } else if (relevantPrices.length >= 2) {
    confidence = 'medium';
    factors.push('Limited recent comparable sales');
  } else {
    factors.push('Very limited comparable sales data');
  }
  
  if (relevantPrices.length > 0) {
    const avgPrice = calculateAverageLandPrice(relevantPrices);
    basePrice = avgPrice.averagePricePerAcre;
  } else {
    // Use regional average if no local data
    const allRecentPrices = getRecentLandPrices(12);
    const avgPrice = calculateAverageLandPrice(allRecentPrices);
    basePrice = avgPrice.averagePricePerAcre;
    factors.push('Using regional average due to lack of local data');
  }
  
  // Apply project type adjustments
  let multiplier = 1;
  if (projectType === 'solar') {
    multiplier = 0.8; // Solar typically uses lower-value land
    factors.push('Solar project discount applied');
  } else if (projectType === 'wind') {
    multiplier = 0.6; // Wind can use agricultural land with lease arrangements
    factors.push('Wind project agricultural compatibility discount');
  } else if (projectType === 'storage') {
    multiplier = 1.2; // Storage needs industrial-grade land
    factors.push('Storage project industrial land premium');
  }
  
  // Apply size discount for large projects
  if (projectSizeAcres > 500) {
    multiplier *= 0.9;
    factors.push('Large project size discount');
  } else if (projectSizeAcres > 1000) {
    multiplier *= 0.8;
    factors.push('Very large project size discount');
  }
  
  const costPerAcre = basePrice * multiplier;
  const estimatedCost = costPerAcre * projectSizeAcres;
  
  return {
    estimatedCost,
    costPerAcre,
    confidence,
    factors
  };
}

// Helper function to calculate distance between two points in kilometers
function calculateDistance(point1: { lat: number; lng: number }, point2: { lat: number; lng: number }): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (point2.lat - point1.lat) * Math.PI / 180;
  const dLng = (point2.lng - point1.lng) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(point1.lat * Math.PI / 180) * Math.cos(point2.lat * Math.PI / 180) * 
    Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}
