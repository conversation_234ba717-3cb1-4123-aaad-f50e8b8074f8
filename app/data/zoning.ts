import { ZoningData } from '../types';

export const mockZoningData: ZoningData[] = [
  {
    id: 'zone-001',
    type: 'industrial',
    subtype: 'Heavy Industrial',
    restrictions: ['Noise limits during night hours', 'Environmental impact assessments required'],
    allowedUses: ['Manufacturing', 'Warehousing', 'Utility-scale renewable energy', 'Research facilities'],
    maxHeight: 150,
    setbackRequirements: 50,
    coordinates: [
      { lat: 37.7700, lng: -122.4200 },
      { lat: 37.7750, lng: -122.4200 },
      { lat: 37.7750, lng: -122.4100 },
      { lat: 37.7700, lng: -122.4100 }
    ]
  },
  {
    id: 'zone-002',
    type: 'agricultural',
    subtype: 'Agricultural Preserve',
    restrictions: ['Maintain agricultural use', 'Limited non-agricultural development'],
    allowedUses: ['Farming', 'Ranching', 'Agrivoltaics', 'Agricultural processing'],
    maxHeight: 35,
    setbackRequirements: 100,
    coordinates: [
      { lat: 37.7600, lng: -122.4300 },
      { lat: 37.7700, lng: -122.4300 },
      { lat: 37.7700, lng: -122.4200 },
      { lat: 37.7600, lng: -122.4200 }
    ]
  },
  {
    id: 'zone-003',
    type: 'commercial',
    subtype: 'General Commercial',
    restrictions: ['Parking requirements', 'Architectural review required'],
    allowedUses: ['Retail', 'Office', 'Small-scale solar installations', 'Electric vehicle charging'],
    maxHeight: 45,
    setbackRequirements: 20,
    coordinates: [
      { lat: 37.7800, lng: -122.4100 },
      { lat: 37.7850, lng: -122.4100 },
      { lat: 37.7850, lng: -122.4000 },
      { lat: 37.7800, lng: -122.4000 }
    ]
  },
  {
    id: 'zone-004',
    type: 'residential',
    subtype: 'Low Density Residential',
    restrictions: ['Single family homes only', 'Height restrictions', 'Density limits'],
    allowedUses: ['Single family homes', 'Rooftop solar', 'Home-based business'],
    maxHeight: 25,
    setbackRequirements: 15,
    coordinates: [
      { lat: 37.7500, lng: -122.4400 },
      { lat: 37.7600, lng: -122.4400 },
      { lat: 37.7600, lng: -122.4300 },
      { lat: 37.7500, lng: -122.4300 }
    ]
  },
  {
    id: 'zone-005',
    type: 'mixed',
    subtype: 'Mixed Use Development',
    restrictions: ['Ground floor commercial required', 'Transit-oriented development'],
    allowedUses: ['Residential', 'Commercial', 'Office', 'Community solar gardens'],
    maxHeight: 75,
    setbackRequirements: 10,
    coordinates: [
      { lat: 37.7850, lng: -122.4000 },
      { lat: 37.7900, lng: -122.4000 },
      { lat: 37.7900, lng: -122.3900 },
      { lat: 37.7850, lng: -122.3900 }
    ]
  },
  {
    id: 'zone-006',
    type: 'industrial',
    subtype: 'Light Industrial',
    restrictions: ['Buffer zones from residential areas', 'Traffic impact studies'],
    allowedUses: ['Light manufacturing', 'Distribution', 'Solar panel manufacturing', 'Battery storage'],
    maxHeight: 60,
    setbackRequirements: 30,
    coordinates: [
      { lat: 37.7400, lng: -122.4500 },
      { lat: 37.7500, lng: -122.4500 },
      { lat: 37.7500, lng: -122.4400 },
      { lat: 37.7400, lng: -122.4400 }
    ]
  },
  {
    id: 'zone-007',
    type: 'agricultural',
    subtype: 'Rangeland',
    restrictions: ['Grazing density limits', 'Erosion control measures'],
    allowedUses: ['Livestock grazing', 'Wind energy', 'Solar grazing', 'Conservation'],
    maxHeight: 200,
    setbackRequirements: 200,
    coordinates: [
      { lat: 37.7300, lng: -122.4600 },
      { lat: 37.7400, lng: -122.4600 },
      { lat: 37.7400, lng: -122.4500 },
      { lat: 37.7300, lng: -122.4500 }
    ]
  },
  {
    id: 'zone-008',
    type: 'commercial',
    subtype: 'Highway Commercial',
    restrictions: ['Highway access requirements', 'Signage restrictions'],
    allowedUses: ['Auto services', 'Truck stops', 'EV charging stations', 'Solar canopies'],
    maxHeight: 35,
    setbackRequirements: 25,
    coordinates: [
      { lat: 37.7900, lng: -122.3900 },
      { lat: 37.7950, lng: -122.3900 },
      { lat: 37.7950, lng: -122.3800 },
      { lat: 37.7900, lng: -122.3800 }
    ]
  },
  {
    id: 'zone-009',
    type: 'residential',
    subtype: 'Medium Density Residential',
    restrictions: ['Density limits', 'Open space requirements'],
    allowedUses: ['Townhomes', 'Condominiums', 'Community solar', 'Microgrids'],
    maxHeight: 40,
    setbackRequirements: 12,
    coordinates: [
      { lat: 37.7200, lng: -122.4700 },
      { lat: 37.7300, lng: -122.4700 },
      { lat: 37.7300, lng: -122.4600 },
      { lat: 37.7200, lng: -122.4600 }
    ]
  },
  {
    id: 'zone-010',
    type: 'industrial',
    subtype: 'Energy Production Zone',
    restrictions: ['Environmental monitoring required', 'Grid connection standards'],
    allowedUses: ['Renewable energy generation', 'Energy storage', 'Grid infrastructure', 'Research'],
    maxHeight: 300,
    setbackRequirements: 100,
    coordinates: [
      { lat: 37.7950, lng: -122.3800 },
      { lat: 37.8000, lng: -122.3800 },
      { lat: 37.8000, lng: -122.3700 },
      { lat: 37.7950, lng: -122.3700 }
    ]
  }
];

export function getZoningForLocation(lat: number, lng: number): ZoningData | null {
  return mockZoningData.find(zone => {
    return isPointInPolygon({ lat, lng }, zone.coordinates);
  }) || null;
}

export function getZoningByType(type: ZoningData['type']): ZoningData[] {
  return mockZoningData.filter(zone => zone.type === type);
}

export function getZonesInArea(bounds: { north: number; south: number; east: number; west: number }): ZoningData[] {
  return mockZoningData.filter(zone => {
    return zone.coordinates.some(coord => 
      coord.lat >= bounds.south && 
      coord.lat <= bounds.north && 
      coord.lng >= bounds.west && 
      coord.lng <= bounds.east
    );
  });
}

export function isRenewableEnergyAllowed(zoneId: string): boolean {
  const zone = mockZoningData.find(z => z.id === zoneId);
  if (!zone) return false;
  
  const renewableKeywords = [
    'renewable energy', 'solar', 'wind', 'agrivoltaics', 
    'energy storage', 'community solar', 'microgrids',
    'energy generation', 'solar grazing', 'EV charging'
  ];
  
  return zone.allowedUses.some(use => 
    renewableKeywords.some(keyword => 
      use.toLowerCase().includes(keyword.toLowerCase())
    )
  );
}

// Helper function to check if a point is inside a polygon
function isPointInPolygon(point: { lat: number; lng: number }, polygon: { lat: number; lng: number }[]): boolean {
  let inside = false;
  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
    if (((polygon[i].lat > point.lat) !== (polygon[j].lat > point.lat)) &&
        (point.lng < (polygon[j].lng - polygon[i].lng) * (point.lat - polygon[i].lat) / (polygon[j].lat - polygon[i].lat) + polygon[i].lng)) {
      inside = !inside;
    }
  }
  return inside;
}
