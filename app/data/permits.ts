import { Permit } from '../types';

export const mockPermits: Permit[] = [
  {
    id: 'permit-001',
    type: 'environmental',
    status: 'approved',
    issueDate: '2023-08-15',
    expiryDate: '2028-08-15',
    authority: 'California Environmental Protection Agency',
    description: 'Environmental Impact Assessment for renewable energy development',
    location: { lat: 37.7749, lng: -122.4194 },
    radius: 5000
  },
  {
    id: 'permit-002',
    type: 'building',
    status: 'pending',
    issueDate: '2024-01-10',
    authority: 'San Francisco Planning Department',
    description: 'Building permit for solar installation infrastructure',
    location: { lat: 37.7849, lng: -122.4094 },
    radius: 2000
  },
  {
    id: 'permit-003',
    type: 'utility',
    status: 'approved',
    issueDate: '2023-11-20',
    expiryDate: '2025-11-20',
    authority: 'Pacific Gas & Electric',
    description: 'Grid interconnection permit for renewable energy facility',
    location: { lat: 37.7649, lng: -122.4294 },
    radius: 3000
  },
  {
    id: 'permit-004',
    type: 'zoning',
    status: 'approved',
    issueDate: '2023-06-05',
    authority: 'Alameda County Planning Department',
    description: 'Conditional use permit for utility-scale solar development',
    location: { lat: 37.7549, lng: -122.4394 },
    radius: 8000
  },
  {
    id: 'permit-005',
    type: 'environmental',
    status: 'rejected',
    issueDate: '2023-12-01',
    authority: 'US Fish and Wildlife Service',
    description: 'Habitat conservation plan - rejected due to endangered species concerns',
    location: { lat: 37.7449, lng: -122.4494 },
    radius: 10000
  },
  {
    id: 'permit-006',
    type: 'building',
    status: 'approved',
    issueDate: '2024-02-14',
    expiryDate: '2026-02-14',
    authority: 'Contra Costa County Building Department',
    description: 'Construction permit for wind turbine foundations',
    location: { lat: 37.7949, lng: -122.3994 },
    radius: 4000
  },
  {
    id: 'permit-007',
    type: 'utility',
    status: 'pending',
    issueDate: '2024-03-01',
    authority: 'California Public Utilities Commission',
    description: 'Power purchase agreement approval for renewable energy project',
    location: { lat: 37.7349, lng: -122.4594 },
    radius: 6000
  },
  {
    id: 'permit-008',
    type: 'environmental',
    status: 'approved',
    issueDate: '2023-09-30',
    expiryDate: '2030-09-30',
    authority: 'California Air Resources Board',
    description: 'Air quality permit for biomass energy facility',
    location: { lat: 37.7149, lng: -122.4794 },
    radius: 7000
  },
  {
    id: 'permit-009',
    type: 'zoning',
    status: 'expired',
    issueDate: '2020-05-15',
    expiryDate: '2023-05-15',
    authority: 'Marin County Planning Commission',
    description: 'Special use permit for renewable energy research facility',
    location: { lat: 37.8049, lng: -122.3894 },
    radius: 3500
  },
  {
    id: 'permit-010',
    type: 'building',
    status: 'approved',
    issueDate: '2024-01-25',
    expiryDate: '2025-01-25',
    authority: 'Solano County Building Department',
    description: 'Electrical permit for solar panel installation',
    location: { lat: 37.7049, lng: -122.4894 },
    radius: 2500
  },
  {
    id: 'permit-011',
    type: 'utility',
    status: 'approved',
    issueDate: '2023-10-12',
    expiryDate: '2028-10-12',
    authority: 'Sacramento Municipal Utility District',
    description: 'Interconnection agreement for distributed solar generation',
    location: { lat: 37.6949, lng: -122.4994 },
    radius: 4500
  },
  {
    id: 'permit-012',
    type: 'environmental',
    status: 'pending',
    issueDate: '2024-02-28',
    authority: 'Regional Water Quality Control Board',
    description: 'Water discharge permit for geothermal energy facility',
    location: { lat: 37.8149, lng: -122.3794 },
    radius: 5500
  },
  {
    id: 'permit-013',
    type: 'zoning',
    status: 'approved',
    issueDate: '2023-07-18',
    expiryDate: '2026-07-18',
    authority: 'Napa County Planning Department',
    description: 'Agricultural preserve compatibility for agrivoltaic project',
    location: { lat: 37.6849, lng: -122.5094 },
    radius: 9000
  },
  {
    id: 'permit-014',
    type: 'building',
    status: 'rejected',
    issueDate: '2024-01-05',
    authority: 'Sonoma County Building Department',
    description: 'Building permit rejected due to fire safety concerns',
    location: { lat: 37.8249, lng: -122.3694 },
    radius: 3000
  },
  {
    id: 'permit-015',
    type: 'utility',
    status: 'approved',
    issueDate: '2023-12-20',
    expiryDate: '2027-12-20',
    authority: 'California Independent System Operator',
    description: 'Transmission interconnection study approval',
    location: { lat: 37.6749, lng: -122.5194 },
    radius: 12000
  }
];

export function getPermitsInArea(center: { lat: number; lng: number }, radiusKm: number): Permit[] {
  return mockPermits.filter(permit => {
    const distance = calculateDistance(center, permit.location);
    return distance <= radiusKm;
  });
}

export function getPermitsByType(type: Permit['type']): Permit[] {
  return mockPermits.filter(permit => permit.type === type);
}

export function getPermitsByStatus(status: Permit['status']): Permit[] {
  return mockPermits.filter(permit => permit.status === status);
}

// Helper function to calculate distance between two points in kilometers
function calculateDistance(point1: { lat: number; lng: number }, point2: { lat: number; lng: number }): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (point2.lat - point1.lat) * Math.PI / 180;
  const dLng = (point2.lng - point1.lng) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(point1.lat * Math.PI / 180) * Math.cos(point2.lat * Math.PI / 180) * 
    Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}
