import { CompetitorProject } from '../types';

export const mockCompetitorProjects: CompetitorProject[] = [
  {
    id: 'comp-001',
    company: 'NextEra Energy',
    projectName: 'Golden Gate Solar Farm',
    type: 'solar',
    status: 'under_construction',
    capacity: 150,
    location: { lat: 37.7649, lng: -122.4294 },
    announcementDate: '2023-06-15',
    expectedCompletion: '2024-12-31',
    investment: 180
  },
  {
    id: 'comp-002',
    company: 'Brookfield Renewable',
    projectName: 'Bay Area Wind Complex',
    type: 'wind',
    status: 'approved',
    capacity: 200,
    location: { lat: 37.7949, lng: -122.3994 },
    announcementDate: '2023-09-20',
    expectedCompletion: '2025-06-30',
    investment: 320
  },
  {
    id: 'comp-003',
    company: 'Sunrun',
    projectName: 'Community Solar Garden Network',
    type: 'solar',
    status: 'operational',
    capacity: 25,
    location: { lat: 37.7849, lng: -122.4094 },
    announcementDate: '2022-03-10',
    expectedCompletion: '2023-08-15',
    investment: 35
  },
  {
    id: 'comp-004',
    company: 'Tesla Energy',
    projectName: 'Megapack Storage Facility',
    type: 'solar',
    status: 'planned',
    capacity: 100,
    location: { lat: 37.7549, lng: -122.4394 },
    announcementDate: '2024-01-25',
    expectedCompletion: '2025-12-31',
    investment: 150
  },
  {
    id: 'comp-005',
    company: 'Orsted',
    projectName: 'Pacific Offshore Wind',
    type: 'wind',
    status: 'approved',
    capacity: 800,
    location: { lat: 37.7749, lng: -122.5500 },
    announcementDate: '2023-11-30',
    expectedCompletion: '2028-12-31',
    investment: 2400
  },
  {
    id: 'comp-006',
    company: 'First Solar',
    projectName: 'Alameda Solar Installation',
    type: 'solar',
    status: 'under_construction',
    capacity: 75,
    location: { lat: 37.7349, lng: -122.4594 },
    announcementDate: '2023-08-12',
    expectedCompletion: '2024-09-30',
    investment: 95
  },
  {
    id: 'comp-007',
    company: 'Pattern Energy',
    projectName: 'Solano Wind Project',
    type: 'wind',
    status: 'operational',
    capacity: 300,
    location: { lat: 37.7049, lng: -122.4894 },
    announcementDate: '2021-05-18',
    expectedCompletion: '2023-03-15',
    investment: 450
  },
  {
    id: 'comp-008',
    company: 'Clearway Energy',
    projectName: 'Napa Valley Agrivoltaics',
    type: 'solar',
    status: 'approved',
    capacity: 50,
    location: { lat: 37.6849, lng: -122.5094 },
    announcementDate: '2023-12-05',
    expectedCompletion: '2025-03-31',
    investment: 75
  },
  {
    id: 'comp-009',
    company: 'Calpine Corporation',
    projectName: 'Geysers Geothermal Expansion',
    type: 'geothermal',
    status: 'under_construction',
    capacity: 120,
    location: { lat: 37.8249, lng: -122.3694 },
    announcementDate: '2023-07-22',
    expectedCompletion: '2025-08-31',
    investment: 200
  },
  {
    id: 'comp-010',
    company: 'Engie North America',
    projectName: 'Contra Costa Battery Storage',
    type: 'solar',
    status: 'planned',
    capacity: 200,
    location: { lat: 37.8049, lng: -122.3894 },
    announcementDate: '2024-02-14',
    expectedCompletion: '2026-01-31',
    investment: 280
  },
  {
    id: 'comp-011',
    company: 'Renewable Energy Systems',
    projectName: 'East Bay Solar Complex',
    type: 'solar',
    status: 'approved',
    capacity: 180,
    location: { lat: 37.6949, lng: -122.4994 },
    announcementDate: '2023-10-08',
    expectedCompletion: '2025-05-31',
    investment: 225
  },
  {
    id: 'comp-012',
    company: 'Vestas',
    projectName: 'Altamont Pass Wind Upgrade',
    type: 'wind',
    status: 'under_construction',
    capacity: 250,
    location: { lat: 37.6749, lng: -122.5194 },
    announcementDate: '2023-04-30',
    expectedCompletion: '2024-11-30',
    investment: 375
  },
  {
    id: 'comp-013',
    company: 'Canadian Solar',
    projectName: 'San Joaquin Solar Farm',
    type: 'solar',
    status: 'cancelled',
    capacity: 300,
    location: { lat: 37.7149, lng: -122.4794 },
    announcementDate: '2022-11-15',
    investment: 400
  },
  {
    id: 'comp-014',
    company: 'Apex Clean Energy',
    projectName: 'Delta Wind Farm',
    type: 'wind',
    status: 'planned',
    capacity: 400,
    location: { lat: 37.6649, lng: -122.5294 },
    announcementDate: '2024-01-10',
    expectedCompletion: '2027-06-30',
    investment: 600
  },
  {
    id: 'comp-015',
    company: 'Recurrent Energy',
    projectName: 'Marin Solar Initiative',
    type: 'solar',
    status: 'approved',
    capacity: 60,
    location: { lat: 37.8149, lng: -122.3794 },
    announcementDate: '2023-09-12',
    expectedCompletion: '2024-12-31',
    investment: 85
  }
];

export function getCompetitorsInArea(center: { lat: number; lng: number }, radiusKm: number): CompetitorProject[] {
  return mockCompetitorProjects.filter(project => {
    const distance = calculateDistance(center, project.location);
    return distance <= radiusKm;
  });
}

export function getCompetitorsByType(type: CompetitorProject['type']): CompetitorProject[] {
  return mockCompetitorProjects.filter(project => project.type === type);
}

export function getCompetitorsByStatus(status: CompetitorProject['status']): CompetitorProject[] {
  return mockCompetitorProjects.filter(project => project.status === status);
}

export function getCompetitorsByCompany(company: string): CompetitorProject[] {
  return mockCompetitorProjects.filter(project => 
    project.company.toLowerCase().includes(company.toLowerCase())
  );
}

export function getRecentCompetitorActivity(monthsBack: number = 12): CompetitorProject[] {
  const cutoffDate = new Date();
  cutoffDate.setMonth(cutoffDate.getMonth() - monthsBack);
  
  return mockCompetitorProjects.filter(project => {
    const announcementDate = new Date(project.announcementDate);
    return announcementDate >= cutoffDate;
  });
}

export function calculateCompetitionDensity(center: { lat: number; lng: number }, radiusKm: number): {
  totalProjects: number;
  totalCapacity: number;
  totalInvestment: number;
  averageProjectSize: number;
  competitionScore: number;
} {
  const competitorsInArea = getCompetitorsInArea(center, radiusKm);
  const activeProjects = competitorsInArea.filter(p => 
    p.status === 'operational' || p.status === 'under_construction' || p.status === 'approved'
  );
  
  const totalProjects = activeProjects.length;
  const totalCapacity = activeProjects.reduce((sum, p) => sum + p.capacity, 0);
  const totalInvestment = activeProjects.reduce((sum, p) => sum + p.investment, 0);
  const averageProjectSize = totalProjects > 0 ? totalCapacity / totalProjects : 0;
  
  // Calculate competition score (0-10, where 10 is highest competition)
  let competitionScore = 0;
  if (totalProjects > 0) {
    const projectDensity = totalProjects / (Math.PI * radiusKm * radiusKm); // projects per km²
    const capacityDensity = totalCapacity / (Math.PI * radiusKm * radiusKm); // MW per km²
    
    // Normalize to 0-10 scale (these thresholds can be adjusted)
    const projectScore = Math.min(10, projectDensity * 100);
    const capacityScore = Math.min(10, capacityDensity * 10);
    
    competitionScore = (projectScore + capacityScore) / 2;
  }
  
  return {
    totalProjects,
    totalCapacity,
    totalInvestment,
    averageProjectSize,
    competitionScore
  };
}

export function getMarketLeaders(): { company: string; totalCapacity: number; projectCount: number }[] {
  const companyStats = mockCompetitorProjects.reduce((acc, project) => {
    if (project.status !== 'cancelled') {
      if (!acc[project.company]) {
        acc[project.company] = { totalCapacity: 0, projectCount: 0 };
      }
      acc[project.company].totalCapacity += project.capacity;
      acc[project.company].projectCount += 1;
    }
    return acc;
  }, {} as Record<string, { totalCapacity: number; projectCount: number }>);
  
  return Object.entries(companyStats)
    .map(([company, stats]) => ({ company, ...stats }))
    .sort((a, b) => b.totalCapacity - a.totalCapacity);
}

export function getProjectsByTimeframe(startDate: string, endDate: string): CompetitorProject[] {
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  return mockCompetitorProjects.filter(project => {
    const announcementDate = new Date(project.announcementDate);
    return announcementDate >= start && announcementDate <= end;
  });
}

// Helper function to calculate distance between two points in kilometers
function calculateDistance(point1: { lat: number; lng: number }, point2: { lat: number; lng: number }): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (point2.lat - point1.lat) * Math.PI / 180;
  const dLng = (point2.lng - point1.lng) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(point1.lat * Math.PI / 180) * Math.cos(point2.lat * Math.PI / 180) * 
    Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}
