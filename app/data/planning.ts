import { PlanningDocument } from '../types';

export const mockPlanningDocuments: PlanningDocument[] = [
  {
    id: 'plan-001',
    title: 'Bay Area Clean Energy Strategic Plan 2024-2030',
    type: 'regional_plan',
    date: '2024-01-15',
    authority: 'Bay Area Air Quality Management District',
    summary: 'Comprehensive plan to achieve carbon neutrality by 2030 through renewable energy deployment, with specific targets for solar and wind installations across the region.',
    relevantAreas: [
      { lat: 37.7749, lng: -122.4194 },
      { lat: 37.8044, lng: -122.2711 },
      { lat: 37.6879, lng: -122.4702 }
    ],
    impact: 'positive',
    score: 9
  },
  {
    id: 'plan-002',
    title: 'California Renewable Portfolio Standard Update',
    type: 'policy_document',
    date: '2023-09-20',
    authority: 'California Public Utilities Commission',
    summary: 'Updated requirements for utilities to source 60% of electricity from renewable sources by 2030, creating strong market demand for new renewable energy projects.',
    relevantAreas: [
      { lat: 37.7749, lng: -122.4194 }
    ],
    impact: 'positive',
    score: 10
  },
  {
    id: 'plan-003',
    title: 'San Francisco Climate Action Plan',
    type: 'regional_plan',
    date: '2023-11-10',
    authority: 'San Francisco Department of Environment',
    summary: 'City-wide plan emphasizing distributed renewable energy, rooftop solar incentives, and community energy programs. Includes streamlined permitting for renewable projects.',
    relevantAreas: [
      { lat: 37.7749, lng: -122.4194 },
      { lat: 37.7849, lng: -122.4094 },
      { lat: 37.7649, lng: -122.4294 }
    ],
    impact: 'positive',
    score: 8
  },
  {
    id: 'plan-004',
    title: 'Agricultural Land Preservation Advisory Report',
    type: 'advisory_report',
    date: '2023-08-05',
    authority: 'Alameda County Agricultural Advisory Committee',
    summary: 'Report recommending restrictions on large-scale solar development on prime agricultural land, but supporting agrivoltaic projects that maintain agricultural productivity.',
    relevantAreas: [
      { lat: 37.7549, lng: -122.4394 },
      { lat: 37.7349, lng: -122.4594 }
    ],
    impact: 'neutral',
    score: 6
  },
  {
    id: 'plan-005',
    title: 'Contra Costa County Energy Infrastructure Master Plan',
    type: 'regional_plan',
    date: '2024-02-28',
    authority: 'Contra Costa County Planning Department',
    summary: 'Long-term plan identifying priority areas for renewable energy development, including designated energy zones with expedited permitting and grid connection support.',
    relevantAreas: [
      { lat: 37.7949, lng: -122.3994 },
      { lat: 37.8049, lng: -122.3894 }
    ],
    impact: 'positive',
    score: 9
  },
  {
    id: 'plan-006',
    title: 'Marin County Wildfire Risk Assessment',
    type: 'advisory_report',
    date: '2023-07-12',
    authority: 'Marin County Fire Department',
    summary: 'Assessment identifying high fire risk areas where new infrastructure development, including renewable energy projects, faces additional safety requirements and potential restrictions.',
    relevantAreas: [
      { lat: 37.8049, lng: -122.3894 },
      { lat: 37.8249, lng: -122.3694 }
    ],
    impact: 'negative',
    score: 4
  },
  {
    id: 'plan-007',
    title: 'Solano County Wind Energy Development Guidelines',
    type: 'policy_document',
    date: '2023-12-01',
    authority: 'Solano County Planning Commission',
    summary: 'Comprehensive guidelines for wind energy development including setback requirements, noise standards, and wildlife protection measures. Generally supportive of wind projects.',
    relevantAreas: [
      { lat: 37.7049, lng: -122.4894 },
      { lat: 37.6949, lng: -122.4994 }
    ],
    impact: 'positive',
    score: 8
  },
  {
    id: 'plan-008',
    title: 'Sacramento Valley Transmission Planning Study',
    type: 'advisory_report',
    date: '2024-01-20',
    authority: 'California Independent System Operator',
    summary: 'Study identifying transmission capacity constraints and upgrade needs to accommodate increased renewable energy generation in the region.',
    relevantAreas: [
      { lat: 37.6949, lng: -122.4994 },
      { lat: 37.6849, lng: -122.5094 }
    ],
    impact: 'neutral',
    score: 7
  },
  {
    id: 'plan-009',
    title: 'Napa County Agricultural Preserve Compatibility Study',
    type: 'advisory_report',
    date: '2023-10-15',
    authority: 'Napa County Agricultural Commissioner',
    summary: 'Study examining compatibility of renewable energy projects with agricultural preserves, recommending agrivoltaic systems and small-scale installations.',
    relevantAreas: [
      { lat: 37.6849, lng: -122.5094 },
      { lat: 37.6749, lng: -122.5194 }
    ],
    impact: 'positive',
    score: 7
  },
  {
    id: 'plan-010',
    title: 'Sonoma County Fire Safety and Energy Infrastructure Report',
    type: 'advisory_report',
    date: '2023-09-30',
    authority: 'Sonoma County Emergency Services',
    summary: 'Report highlighting fire safety concerns with energy infrastructure and recommending enhanced safety measures for renewable energy projects in high-risk areas.',
    relevantAreas: [
      { lat: 37.8249, lng: -122.3694 }
    ],
    impact: 'negative',
    score: 3
  },
  {
    id: 'plan-011',
    title: 'Bay Area Regional Energy Resilience Strategy',
    type: 'regional_plan',
    date: '2024-03-10',
    authority: 'Association of Bay Area Governments',
    summary: 'Regional strategy promoting distributed energy resources, microgrids, and community energy systems to enhance grid resilience and reduce wildfire risks.',
    relevantAreas: [
      { lat: 37.7749, lng: -122.4194 },
      { lat: 37.7849, lng: -122.4094 },
      { lat: 37.7949, lng: -122.3994 },
      { lat: 37.8049, lng: -122.3894 }
    ],
    impact: 'positive',
    score: 9
  },
  {
    id: 'plan-012',
    title: 'California Environmental Justice Screening Tool Update',
    type: 'policy_document',
    date: '2023-11-25',
    authority: 'California Environmental Protection Agency',
    summary: 'Updated screening tool for identifying disadvantaged communities that should receive priority for clean energy investments and environmental benefits.',
    relevantAreas: [
      { lat: 37.7749, lng: -122.4194 }
    ],
    impact: 'positive',
    score: 8
  }
];

export function getPlanningDocumentsForArea(center: { lat: number; lng: number }, radiusKm: number): PlanningDocument[] {
  return mockPlanningDocuments.filter(doc => {
    return doc.relevantAreas.some(area => {
      const distance = calculateDistance(center, area);
      return distance <= radiusKm;
    });
  });
}

export function getPlanningDocumentsByType(type: PlanningDocument['type']): PlanningDocument[] {
  return mockPlanningDocuments.filter(doc => doc.type === type);
}

export function getPlanningDocumentsByImpact(impact: PlanningDocument['impact']): PlanningDocument[] {
  return mockPlanningDocuments.filter(doc => doc.impact === impact);
}

export function getRecentPlanningDocuments(monthsBack: number = 12): PlanningDocument[] {
  const cutoffDate = new Date();
  cutoffDate.setMonth(cutoffDate.getMonth() - monthsBack);
  
  return mockPlanningDocuments.filter(doc => {
    const docDate = new Date(doc.date);
    return docDate >= cutoffDate;
  });
}

export function calculatePlanningScore(documents: PlanningDocument[]): number {
  if (documents.length === 0) return 5; // Neutral score if no documents
  
  const weightedScore = documents.reduce((total, doc) => {
    let weight = 1;
    if (doc.type === 'regional_plan') weight = 1.5;
    if (doc.type === 'policy_document') weight = 1.3;
    
    let impactMultiplier = 1;
    if (doc.impact === 'positive') impactMultiplier = 1;
    if (doc.impact === 'negative') impactMultiplier = -0.5;
    if (doc.impact === 'neutral') impactMultiplier = 0.3;
    
    return total + (doc.score * weight * impactMultiplier);
  }, 0);
  
  const totalWeight = documents.reduce((total, doc) => {
    let weight = 1;
    if (doc.type === 'regional_plan') weight = 1.5;
    if (doc.type === 'policy_document') weight = 1.3;
    return total + weight;
  }, 0);
  
  return Math.max(0, Math.min(10, weightedScore / totalWeight));
}

// Helper function to calculate distance between two points in kilometers
function calculateDistance(point1: { lat: number; lng: number }, point2: { lat: number; lng: number }): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (point2.lat - point1.lat) * Math.PI / 180;
  const dLng = (point2.lng - point1.lng) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(point1.lat * Math.PI / 180) * Math.cos(point2.lat * Math.PI / 180) * 
    Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}
