import { NewsArticle } from '../types';

export const mockNewsArticles: NewsArticle[] = [
  {
    id: 'news-001',
    title: 'California Approves $2 Billion in Clean Energy Incentives',
    source: 'San Francisco Chronicle',
    date: '2024-03-15',
    summary: 'State legislature approves major funding package for renewable energy projects, including streamlined permitting and tax incentives for solar and wind developments.',
    sentiment: 'positive',
    relevance: 9,
    location: { lat: 37.7749, lng: -122.4194 },
    tags: ['policy', 'incentives', 'solar', 'wind', 'permitting']
  },
  {
    id: 'news-002',
    title: 'Local Community Opposes Proposed Solar Farm in Agricultural Area',
    source: 'Alameda County News',
    date: '2024-02-28',
    summary: 'Residents and farmers express concerns about 500-acre solar installation, citing impacts on agricultural land and rural character of the community.',
    sentiment: 'negative',
    relevance: 7,
    location: { lat: 37.7549, lng: -122.4394 },
    tags: ['community opposition', 'agriculture', 'solar', 'land use']
  },
  {
    id: 'news-003',
    title: 'PG&E Announces Major Grid Modernization Project',
    source: 'Energy News Network',
    date: '2024-03-10',
    summary: 'Utility company plans $1.5 billion investment in grid infrastructure to support increased renewable energy integration and improve reliability.',
    sentiment: 'positive',
    relevance: 8,
    location: { lat: 37.7749, lng: -122.4194 },
    tags: ['grid', 'infrastructure', 'utility', 'renewable integration']
  },
  {
    id: 'news-004',
    title: 'Wind Farm Project Delayed Due to Environmental Review',
    source: 'Contra Costa Times',
    date: '2024-02-20',
    summary: 'Proposed 200MW wind farm faces additional environmental studies after concerns raised about impacts on migratory bird populations.',
    sentiment: 'negative',
    relevance: 6,
    location: { lat: 37.7949, lng: -122.3994 },
    tags: ['wind', 'environmental review', 'wildlife', 'delays']
  },
  {
    id: 'news-005',
    title: 'San Francisco Launches Community Solar Program',
    source: 'SF Gate',
    date: '2024-03-05',
    summary: 'City announces new program allowing residents to subscribe to shared solar installations, making clean energy accessible to renters and those without suitable rooftops.',
    sentiment: 'positive',
    relevance: 8,
    location: { lat: 37.7749, lng: -122.4194 },
    tags: ['community solar', 'municipal program', 'accessibility']
  },
  {
    id: 'news-006',
    title: 'Fire Safety Concerns Halt Energy Project Approvals',
    source: 'Marin Independent Journal',
    date: '2024-01-25',
    summary: 'County commissioners postpone decisions on three renewable energy projects pending updated fire safety assessments and evacuation planning.',
    sentiment: 'negative',
    relevance: 7,
    location: { lat: 37.8049, lng: -122.3894 },
    tags: ['fire safety', 'project delays', 'county approval']
  },
  {
    id: 'news-007',
    title: 'Tech Giants Partner on Massive Solar Initiative',
    source: 'Silicon Valley Business Journal',
    date: '2024-03-12',
    summary: 'Apple, Google, and Meta announce joint venture to develop 1GW of solar capacity across Northern California, creating significant demand for renewable energy projects.',
    sentiment: 'positive',
    relevance: 9,
    location: { lat: 37.7749, lng: -122.4194 },
    tags: ['corporate procurement', 'solar', 'tech companies', 'large scale']
  },
  {
    id: 'news-008',
    title: 'Agrivoltaics Pilot Project Shows Promising Results',
    source: 'California Farmer',
    date: '2024-02-15',
    summary: 'Three-year study demonstrates that combining solar panels with agriculture can increase crop yields while generating clean energy, opening new opportunities for farmers.',
    sentiment: 'positive',
    relevance: 8,
    location: { lat: 37.6849, lng: -122.5094 },
    tags: ['agrivoltaics', 'agriculture', 'innovation', 'dual use']
  },
  {
    id: 'news-009',
    title: 'Battery Storage Facility Approved Despite Neighbor Concerns',
    source: 'East Bay Times',
    date: '2024-01-30',
    summary: 'Planning commission approves 100MWh battery storage project after heated public hearings, with conditions for enhanced safety monitoring and community benefits.',
    sentiment: 'neutral',
    relevance: 6,
    location: { lat: 37.7049, lng: -122.4894 },
    tags: ['battery storage', 'community concerns', 'approval', 'safety']
  },
  {
    id: 'news-010',
    title: 'Federal Tax Credits Extended for Renewable Energy',
    source: 'Reuters',
    date: '2024-03-08',
    summary: 'Congress extends Investment Tax Credit and Production Tax Credit for solar and wind projects through 2032, providing long-term certainty for developers.',
    sentiment: 'positive',
    relevance: 10,
    location: { lat: 37.7749, lng: -122.4194 },
    tags: ['federal policy', 'tax credits', 'solar', 'wind', 'incentives']
  },
  {
    id: 'news-011',
    title: 'Transmission Line Upgrade Enables More Renewable Connections',
    source: 'Utility Dive',
    date: '2024-02-22',
    summary: 'Completion of 230kV transmission line upgrade increases grid capacity for renewable energy projects in Solano and Napa counties by 300MW.',
    sentiment: 'positive',
    relevance: 7,
    location: { lat: 37.6949, lng: -122.4994 },
    tags: ['transmission', 'grid capacity', 'infrastructure', 'renewable connection']
  },
  {
    id: 'news-012',
    title: 'Environmental Groups Challenge Solar Project Permits',
    source: 'Environmental Law & Policy Center',
    date: '2024-01-18',
    summary: 'Coalition files lawsuit challenging permits for 800MW solar project, citing inadequate environmental impact assessment and threats to endangered species habitat.',
    sentiment: 'negative',
    relevance: 6,
    location: { lat: 37.7449, lng: -122.4494 },
    tags: ['environmental opposition', 'lawsuit', 'permits', 'endangered species']
  },
  {
    id: 'news-013',
    title: 'Offshore Wind Lease Auction Generates Record Bids',
    source: 'Offshore Wind Magazine',
    date: '2024-03-01',
    summary: 'Federal auction for offshore wind development rights off Northern California coast raises $1.8 billion, signaling strong industry interest in the region.',
    sentiment: 'positive',
    relevance: 8,
    location: { lat: 37.7749, lng: -122.4194 },
    tags: ['offshore wind', 'federal lease', 'auction', 'industry interest']
  },
  {
    id: 'news-014',
    title: 'Rural Counties Embrace Clean Energy Economic Development',
    source: 'California Rural Development Council',
    date: '2024-02-10',
    summary: 'Study shows renewable energy projects have brought $500 million in economic benefits to rural counties, including jobs, tax revenue, and land lease payments.',
    sentiment: 'positive',
    relevance: 7,
    location: { lat: 37.7349, lng: -122.4594 },
    tags: ['economic development', 'rural communities', 'jobs', 'tax revenue']
  },
  {
    id: 'news-015',
    title: 'Grid Reliability Concerns Prompt Energy Storage Requirements',
    source: 'California Public Utilities Commission',
    date: '2024-01-12',
    summary: 'New regulations require renewable energy projects over 20MW to include energy storage or demonstrate grid stability contributions.',
    sentiment: 'neutral',
    relevance: 8,
    location: { lat: 37.7749, lng: -122.4194 },
    tags: ['grid reliability', 'energy storage', 'regulations', 'requirements']
  }
];

export function getNewsForArea(center: { lat: number; lng: number }, radiusKm: number): NewsArticle[] {
  return mockNewsArticles.filter(article => {
    if (!article.location) return false;
    const distance = calculateDistance(center, article.location);
    return distance <= radiusKm;
  });
}

export function getNewsBySentiment(sentiment: NewsArticle['sentiment']): NewsArticle[] {
  return mockNewsArticles.filter(article => article.sentiment === sentiment);
}

export function getRecentNews(daysBack: number = 30): NewsArticle[] {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysBack);
  
  return mockNewsArticles.filter(article => {
    const articleDate = new Date(article.date);
    return articleDate >= cutoffDate;
  });
}

export function getNewsByTags(tags: string[]): NewsArticle[] {
  return mockNewsArticles.filter(article => 
    tags.some(tag => 
      article.tags.some(articleTag => 
        articleTag.toLowerCase().includes(tag.toLowerCase())
      )
    )
  );
}

export function calculateNewsSentimentScore(articles: NewsArticle[]): number {
  if (articles.length === 0) return 5; // Neutral score if no articles
  
  const sentimentValues = {
    positive: 1,
    neutral: 0,
    negative: -1
  };
  
  const weightedScore = articles.reduce((total, article) => {
    const sentimentValue = sentimentValues[article.sentiment];
    const weight = article.relevance / 10; // Normalize relevance to 0-1
    return total + (sentimentValue * weight);
  }, 0);
  
  const totalWeight = articles.reduce((total, article) => {
    return total + (article.relevance / 10);
  }, 0);
  
  if (totalWeight === 0) return 5;
  
  const normalizedScore = weightedScore / totalWeight;
  // Convert from -1 to 1 scale to 0 to 10 scale
  return Math.max(0, Math.min(10, (normalizedScore + 1) * 5));
}

export function getTopRelevantNews(articles: NewsArticle[], limit: number = 5): NewsArticle[] {
  return articles
    .sort((a, b) => b.relevance - a.relevance)
    .slice(0, limit);
}

// Helper function to calculate distance between two points in kilometers
function calculateDistance(point1: { lat: number; lng: number }, point2: { lat: number; lng: number }): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (point2.lat - point1.lat) * Math.PI / 180;
  const dLng = (point2.lng - point1.lng) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(point1.lat * Math.PI / 180) * Math.cos(point2.lat * Math.PI / 180) * 
    Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}
