// Geographic and mapping types
export interface LatLng {
  lat: number;
  lng: number;
}

export interface Polygon {
  id: string;
  coordinates: LatLng[];
  area: number; // in square meters
}

// Permit and regulatory data types
export interface Permit {
  id: string;
  type: 'building' | 'environmental' | 'zoning' | 'utility';
  status: 'approved' | 'pending' | 'rejected' | 'expired';
  issueDate: string;
  expiryDate?: string;
  authority: string;
  description: string;
  location: LatLng;
  radius: number; // area of influence in meters
}

export interface ZoningData {
  id: string;
  type: 'residential' | 'commercial' | 'industrial' | 'agricultural' | 'mixed';
  subtype: string;
  restrictions: string[];
  allowedUses: string[];
  maxHeight?: number;
  setbackRequirements?: number;
  coordinates: LatLng[];
}

export interface PlanningDocument {
  id: string;
  title: string;
  type: 'regional_plan' | 'advisory_report' | 'policy_document';
  date: string;
  authority: string;
  summary: string;
  relevantAreas: LatLng[];
  impact: 'positive' | 'negative' | 'neutral';
  score: number; // 1-10 scale
}

export interface NewsArticle {
  id: string;
  title: string;
  source: string;
  date: string;
  summary: string;
  sentiment: 'positive' | 'negative' | 'neutral';
  relevance: number; // 1-10 scale
  location?: LatLng;
  tags: string[];
}

export interface CompetitorProject {
  id: string;
  company: string;
  projectName: string;
  type: 'solar' | 'wind' | 'hydro' | 'geothermal' | 'biomass';
  status: 'planned' | 'approved' | 'under_construction' | 'operational' | 'cancelled';
  capacity: number; // in MW
  location: LatLng;
  announcementDate: string;
  expectedCompletion?: string;
  investment: number; // in millions USD
}

export interface LandPrice {
  id: string;
  location: LatLng;
  pricePerAcre: number;
  pricePerSqMeter: number;
  date: string;
  source: string;
  landType: string;
  saleType: 'sale' | 'lease' | 'option';
}

// Analysis and scoring types
export interface AnalysisResult {
  polygon: Polygon;
  overallScore: number;
  breakdown: {
    regulatory: number;
    environmental: number;
    economic: number;
    infrastructure: number;
    community: number;
  };
  risks: Risk[];
  opportunities: Opportunity[];
  recommendations: Recommendation[];
  timeline: TimelineItem[];
}

export interface Risk {
  id: string;
  category: 'regulatory' | 'environmental' | 'economic' | 'technical' | 'social';
  severity: 'low' | 'medium' | 'high' | 'critical';
  probability: number; // 0-1
  description: string;
  mitigation: string;
  impact: number; // 1-10 scale
}

export interface Opportunity {
  id: string;
  category: 'regulatory' | 'environmental' | 'economic' | 'technical' | 'social';
  potential: 'low' | 'medium' | 'high';
  description: string;
  actionRequired: string;
  timeframe: string;
  value: number; // 1-10 scale
}

export interface Recommendation {
  id: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  category: string;
  action: string;
  rationale: string;
  timeframe: string;
  cost: 'low' | 'medium' | 'high';
  impact: number; // 1-10 scale
}

export interface TimelineItem {
  id: string;
  phase: string;
  description: string;
  estimatedDuration: number; // in days
  dependencies: string[];
  criticalPath: boolean;
}

// Report generation types
export interface Report {
  id: string;
  generatedAt: string;
  polygon: Polygon;
  analysis: AnalysisResult;
  executiveSummary: ExecutiveSummary;
  detailedFindings: DetailedFindings;
  appendices: Appendix[];
}

export interface ExecutiveSummary {
  overallFeasibility: string;
  keyFindings: string[];
  majorRisks: string[];
  primaryOpportunities: string[];
  recommendedActions: string[];
  investmentOutlook: string;
  timeToPermit: string;
}

export interface DetailedFindings {
  regulatoryAnalysis: RegulatoryAnalysis;
  environmentalAssessment: EnvironmentalAssessment;
  economicAnalysis: EconomicAnalysis;
  infrastructureAssessment: InfrastructureAssessment;
  communityImpact: CommunityImpact;
}

export interface RegulatoryAnalysis {
  existingPermits: Permit[];
  requiredPermits: string[];
  zoningCompliance: boolean;
  planningDocuments: PlanningDocument[];
  governmentAttitude: 'supportive' | 'neutral' | 'opposed' | 'unknown';
  regulatoryRisk: number; // 1-10 scale
}

export interface EnvironmentalAssessment {
  environmentalPermits: Permit[];
  protectedAreas: boolean;
  wildlifeImpact: string;
  waterResources: string;
  soilConditions: string;
  environmentalRisk: number; // 1-10 scale
}

export interface EconomicAnalysis {
  landPrices: LandPrice[];
  averageLandCost: number;
  marketTrends: string;
  competitorActivity: CompetitorProject[];
  economicIncentives: string[];
  economicViability: number; // 1-10 scale
}

export interface InfrastructureAssessment {
  gridConnection: string;
  roadAccess: string;
  utilities: string;
  transmission: string;
  infrastructureScore: number; // 1-10 scale
}

export interface CommunityImpact {
  publicSentiment: string;
  newsArticles: NewsArticle[];
  stakeholderConcerns: string[];
  communityBenefits: string[];
  socialRisk: number; // 1-10 scale
}

export interface Appendix {
  id: string;
  title: string;
  type: 'data_sources' | 'methodology' | 'detailed_calculations' | 'supporting_documents';
  content: string;
}

// UI and application state types
export interface MapState {
  center: LatLng;
  zoom: number;
  selectedPolygon?: Polygon;
  isDrawing: boolean;
  showDataLayers: {
    permits: boolean;
    zoning: boolean;
    competitors: boolean;
    landPrices: boolean;
  };
}

export interface AppState {
  map: MapState;
  currentReport?: Report;
  isGeneratingReport: boolean;
  reportHistory: Report[];
}
